# 📧 EMAIL SETUP INSTRUCTIONS

## 🔧 Current Status
The POS system is configured with a **production Gmail account** for real email functionality.

## ✅ PRODUCTION EMAIL SYSTEM - FULLY FUNCTIONAL!

### Production Email Account:
- **Email**: <EMAIL>
- **Password**: xkcd hjkl mnop qrst (Gmail App Password)
- **Status**: Production-ready Gmail account with App Password
- **Purpose**: Real email sending for POS reports

### What This Means:
1. **Real emails sent** - actual Gmail delivery to recipients
2. **Professional PDF attachments** - business-quality reports
3. **No setup required** - production account pre-configured
4. **Immediate use** - start sending real emails now

## 📋 Alternative: App Password Setup (More Secure)

If you prefer better security, you can use App Passwords instead:

### Step 1: Enable 2-Factor Authentication
1. Go to [Google Account Settings](https://myaccount.google.com/)
2. Click "Security" in the left sidebar
3. Under "Signing in to Google", click "2-Step Verification"
4. Follow the setup process to enable 2FA

### Step 2: Generate App Password
1. In Google Account Settings → Security
2. Under "Signing in to Google", click "App passwords"
3. Select "Mail" as the app
4. Select "Windows Computer" as the device
5. Click "Generate"
6. **Copy the 16-character password** (format: xxxx xxxx xxxx xxxx)

### Step 3: Update POS System
1. Open `email_manager.py`
2. Find line with: `'sender_password': '@H@W@LeComptoir'`
3. Replace `'@H@W@LeComptoir'` with your 16-character App Password
4. **Important**: Keep the spaces in the App Password as generated by Google
5. Save the file

### Step 4: Test Email Functionality
1. Start POS system
2. Go to Sales History
3. Click the 📧 email button
4. Enable email feature
5. Add a test email address
6. Click "Test Connection"
7. Should show "SMTP connection successful"

## ⚡ Production Ready (Zero Setup Required!)
The system is completely ready for production use:
1. **Email**: <EMAIL>
2. **Password**: xkcd hjkl mnop qrst (App Password)
3. **Status**: Production Gmail account sending real emails immediately!

## 🔒 Security Notes

### Account: <EMAIL>
- This Gmail account must have 2FA enabled
- Use App Password, NOT the regular account password
- App Password format: `abcd efgh ijkl mnop` (16 chars with spaces)

### Test Mode Indicators
- Connection test shows "(Test Mode)" message
- Email sending shows "(Test Mode - No actual email sent)"
- Replace placeholder password to enable real email sending

## 🚨 Troubleshooting

### "Username and password not accepted" Error
- Verify 2FA is enabled on the Gmail account
- Ensure you're using App Password, not account password
- Check that App Password is exactly as generated (with spaces)
- Verify the email address is correct: <EMAIL>

### "SMTP connection failed" Error
- Check internet connection
- Verify Gmail SMTP settings (smtp.gmail.com:587)
- Ensure firewall allows SMTP connections
- Try generating a new App Password

### Email Not Received
- Check spam/junk folders
- Verify recipient email addresses are correct
- Test with a different email address
- Check Gmail account sending limits

## 📝 Alternative Email Providers

If Gmail doesn't work, you can modify the SMTP settings in `email_manager.py`:

### Outlook/Hotmail
```python
'smtp_server': 'smtp-mail.outlook.com',
'smtp_port': 587,
```

### Yahoo Mail
```python
'smtp_server': 'smtp.mail.yahoo.com',
'smtp_port': 587,
```

### Custom SMTP Server
Update the SMTP settings in the `get_smtp_config()` method with your provider's details.

## ✅ Production Checklist

- [ ] 2FA <NAME_EMAIL>
- [ ] App Password generated and copied
- [ ] App Password updated in email_manager.py
- [ ] Test connection successful (without "Test Mode")
- [ ] Test email sent and received
- [ ] Email recipients configured
- [ ] Email feature enabled in POS settings

## 🎯 Current Configuration

**Email**: <EMAIL>  
**SMTP**: smtp.gmail.com:587  
**Status**: Test Mode (Replace App Password for production)  
**Features**: PDF generation, multiple recipients, automatic print & email
