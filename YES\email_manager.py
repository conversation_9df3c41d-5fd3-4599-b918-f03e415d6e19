import smtplib
import ssl
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MI<PERSON>Base
from email import encoders
import os
from datetime import datetime
from database import safe_db_connection
import base64
from cryptography.fernet import Fernet
import hashlib

class EmailManager:
    def __init__(self):
        self.encryption_key = self._get_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        
    def _get_encryption_key(self):
        """Generate encryption key based on system info"""
        import platform
        system_info = f"{platform.node()}{platform.system()}{platform.processor()}"
        key_material = hashlib.sha256(system_info.encode()).digest()
        return base64.urlsafe_b64encode(key_material)
    
    def encrypt_password(self, password):
        """Encrypt password for storage"""
        try:
            return self.cipher_suite.encrypt(password.encode()).decode()
        except:
            return password  # Fallback to plain text if encryption fails
    
    def decrypt_password(self, encrypted_password):
        """Decrypt password for use"""
        try:
            return self.cipher_suite.decrypt(encrypted_password.encode()).decode()
        except:
            return encrypted_password  # Assume it's already plain text
    
    def get_smtp_config(self):
        """Get SMTP configuration from database"""
        try:
            with safe_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT * FROM smtp_config WHERE id = 1")
                config = c.fetchone()
                
                if config:
                    return {
                        'smtp_server': config['smtp_server'],
                        'smtp_port': config['smtp_port'],
                        'sender_email': config['sender_email'],
                        'sender_password': self.decrypt_password(config['sender_password']) if config['sender_password'] else '',
                        'email_enabled': bool(config['email_enabled'])
                    }
                else:
                    return self.get_default_smtp_config()
        except:
            return self.get_default_smtp_config()
    
    def get_default_smtp_config(self):
        """Get default SMTP configuration"""
        return {
            'smtp_server': 'smtp.gmail.com',
            'smtp_port': 587,
            'sender_email': '',
            'sender_password': '',
            'email_enabled': False
        }
    
    def save_smtp_config(self, config):
        """Save SMTP configuration to database"""
        try:
            with safe_db_connection() as conn:
                c = conn.cursor()
                
                # Encrypt password before storing
                encrypted_password = self.encrypt_password(config['sender_password']) if config['sender_password'] else ''
                
                # Check if config exists
                c.execute("SELECT id FROM smtp_config WHERE id = 1")
                exists = c.fetchone()
                
                if exists:
                    c.execute("""
                        UPDATE smtp_config 
                        SET smtp_server = ?, smtp_port = ?, sender_email = ?, 
                            sender_password = ?, email_enabled = ?
                        WHERE id = 1
                    """, (config['smtp_server'], config['smtp_port'], config['sender_email'],
                          encrypted_password, int(config['email_enabled'])))
                else:
                    c.execute("""
                        INSERT INTO smtp_config (id, smtp_server, smtp_port, sender_email, 
                                                sender_password, email_enabled)
                        VALUES (1, ?, ?, ?, ?, ?)
                    """, (config['smtp_server'], config['smtp_port'], config['sender_email'],
                          encrypted_password, int(config['email_enabled'])))
                
                conn.commit()
                return True
        except Exception as e:
            print(f"Error saving SMTP config: {e}")
            return False
    
    def get_email_addresses(self):
        """Get list of email addresses from database"""
        try:
            with safe_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT email_address FROM email_settings WHERE is_active = 1 ORDER BY email_address")
                emails = c.fetchall()
                return [email['email_address'] for email in emails]
        except:
            return []
    
    def add_email_address(self, email_address, user_id=None):
        """Add email address to database"""
        try:
            with safe_db_connection() as conn:
                c = conn.cursor()
                
                # Check if email already exists
                c.execute("SELECT id FROM email_settings WHERE email_address = ?", (email_address,))
                if c.fetchone():
                    return False, "Email address already exists"
                
                c.execute("""
                    INSERT INTO email_settings (email_address, is_active, added_date, added_by_user_id)
                    VALUES (?, 1, ?, ?)
                """, (email_address, datetime.now().isoformat(), user_id))
                
                conn.commit()
                return True, "Email address added successfully"
        except Exception as e:
            return False, f"Error adding email: {e}"
    
    def remove_email_address(self, email_address):
        """Remove email address from database"""
        try:
            with safe_db_connection() as conn:
                c = conn.cursor()
                c.execute("DELETE FROM email_settings WHERE email_address = ?", (email_address,))
                conn.commit()
                return True
        except Exception as e:
            print(f"Error removing email: {e}")
            return False
    
    def validate_email(self, email):
        """Validate email address format"""
        import re
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    def test_smtp_connection(self, config):
        """Test SMTP connection with given configuration"""
        try:
            context = ssl.create_default_context()
            with smtplib.SMTP(config['smtp_server'], config['smtp_port']) as server:
                server.starttls(context=context)
                server.login(config['sender_email'], config['sender_password'])
                return True, "SMTP connection successful"
        except Exception as e:
            return False, f"SMTP connection failed: {str(e)}"
    
    def send_history_email(self, pdf_path, subject, body, business_name="POS Business"):
        """Send history report email with PDF attachment"""
        smtp_config = self.get_smtp_config()
        
        if not smtp_config['email_enabled']:
            return False, "Email feature is disabled"
        
        if not smtp_config['sender_email'] or not smtp_config['sender_password']:
            return False, "Email configuration incomplete"
        
        email_addresses = self.get_email_addresses()
        if not email_addresses:
            return False, "No email addresses configured"
        
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = smtp_config['sender_email']
            msg['To'] = ", ".join(email_addresses)
            msg['Subject'] = subject
            
            # Add body
            msg.attach(MIMEText(body, 'plain'))
            
            # Add PDF attachment
            if os.path.exists(pdf_path):
                with open(pdf_path, "rb") as attachment:
                    part = MIMEBase('application', 'octet-stream')
                    part.set_payload(attachment.read())
                
                encoders.encode_base64(part)
                part.add_header(
                    'Content-Disposition',
                    f'attachment; filename= {os.path.basename(pdf_path)}'
                )
                msg.attach(part)
            
            # Send email
            context = ssl.create_default_context()
            with smtplib.SMTP(smtp_config['smtp_server'], smtp_config['smtp_port']) as server:
                server.starttls(context=context)
                server.login(smtp_config['sender_email'], smtp_config['sender_password'])
                server.sendmail(smtp_config['sender_email'], email_addresses, msg.as_string())
            
            return True, f"Email sent successfully to {len(email_addresses)} recipients"
            
        except Exception as e:
            return False, f"Failed to send email: {str(e)}"
