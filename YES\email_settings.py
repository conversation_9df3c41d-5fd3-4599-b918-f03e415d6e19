import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from email_manager import EmailManager
import threading

class EmailSettingsWindow:
    def __init__(self, parent, app):
        self.parent = parent
        self.app = app
        self.email_manager = EmailManager()
        
        # Create popup window
        self.window = tk.Toplevel(parent)
        self.window.title(self.app.get_text('email_settings'))
        self.window.geometry("500x400")
        self.window.configure(bg='#2b2b2b')
        self.window.resizable(False, False)
        
        # Make window modal
        self.window.transient(parent)
        self.window.grab_set()
        
        # Center window
        self.center_window()
        
        # Apply theme
        self.apply_theme()
        
        # Create UI
        self.create_widgets()
        
        # Load current settings
        self.load_settings()
        
        # Focus window
        self.window.focus_set()
    
    def center_window(self):
        """Center the window on screen"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.window.winfo_screenheight() // 2) - (400 // 2)
        self.window.geometry(f"500x400+{x}+{y}")
    
    def apply_theme(self):
        """Apply dark theme to window"""
        style = ttk.Style()
        
        # Configure styles for dark theme
        style.configure('Dark.TFrame', background='#2b2b2b')
        style.configure('Dark.TLabel', background='#2b2b2b', foreground='white', font=('Segoe UI', 10))
        style.configure('Dark.TButton', background='#ff8000', foreground='white', font=('Segoe UI', 10))
        style.configure('Dark.TEntry', fieldbackground='#404040', foreground='white', font=('Segoe UI', 10))
        style.configure('Dark.TCheckbutton', background='#2b2b2b', foreground='white', font=('Segoe UI', 10))
        style.configure('Dark.TCombobox', fieldbackground='#404040', foreground='white', font=('Segoe UI', 10))
        
        # Configure Listbox colors
        self.window.option_add('*TCombobox*Listbox.Background', '#404040')
        self.window.option_add('*TCombobox*Listbox.Foreground', 'white')
    
    def create_widgets(self):
        """Create all UI widgets"""
        # Main container
        main_frame = ttk.Frame(self.window, style='Dark.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = ttk.Label(main_frame, text=self.app.get_text('email_settings'),
                               style='Dark.TLabel', font=('Segoe UI', 16, 'bold'))
        title_label.pack(pady=(0, 20))

        # Email Configuration Section
        config_frame = ttk.LabelFrame(main_frame, text="Email Configuration", style='Dark.TFrame')
        config_frame.pack(fill=tk.X, pady=(0, 20))

        # Enable email checkbox
        self.email_enabled_var = tk.BooleanVar()
        self.email_enabled_cb = ttk.Checkbutton(config_frame, text="Enable Email Feature",
                                               variable=self.email_enabled_var,
                                               style='Dark.TCheckbutton',
                                               command=self.toggle_email_feature)
        self.email_enabled_cb.pack(anchor=tk.W, padx=10, pady=10)

        # Info label
        info_label = ttk.Label(config_frame, text="Sender: <EMAIL>",
                              style='Dark.TLabel', font=('Segoe UI', 9, 'italic'))
        info_label.pack(anchor=tk.W, padx=10, pady=(0, 10))

        # Test connection button
        self.test_button = ttk.Button(config_frame, text="Test Connection",
                                     style='Dark.TButton', command=self.test_connection)
        self.test_button.pack(pady=10)
        
        # Email Addresses Section
        addresses_frame = ttk.LabelFrame(main_frame, text="Email Recipients", style='Dark.TFrame')
        addresses_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # Add email section
        add_frame = ttk.Frame(addresses_frame, style='Dark.TFrame')
        add_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(add_frame, text="Add Email:", style='Dark.TLabel').pack(side=tk.LEFT)
        self.new_email_var = tk.StringVar()
        self.new_email_entry = ttk.Entry(add_frame, textvariable=self.new_email_var, 
                                        style='Dark.TEntry', width=30)
        self.new_email_entry.pack(side=tk.LEFT, padx=(10, 5))
        self.new_email_entry.bind('<Return>', lambda e: self.add_email())
        
        self.add_email_button = ttk.Button(add_frame, text="Add", style='Dark.TButton', 
                                          command=self.add_email)
        self.add_email_button.pack(side=tk.LEFT)
        
        # Email list
        list_frame = ttk.Frame(addresses_frame, style='Dark.TFrame')
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Create listbox with scrollbar
        self.email_listbox = tk.Listbox(list_frame, bg='#404040', fg='white', 
                                       font=('Segoe UI', 10), selectbackground='#ff8000')
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.email_listbox.yview)
        self.email_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.email_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Remove email button
        self.remove_email_button = ttk.Button(addresses_frame, text="Remove Selected", 
                                             style='Dark.TButton', command=self.remove_email)
        self.remove_email_button.pack(pady=5)
        
        # Bottom buttons
        button_frame = ttk.Frame(main_frame, style='Dark.TFrame')
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        self.save_button = ttk.Button(button_frame, text="Save Settings", 
                                     style='Dark.TButton', command=self.save_settings)
        self.save_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.cancel_button = ttk.Button(button_frame, text="Cancel", 
                                       style='Dark.TButton', command=self.close_window)
        self.cancel_button.pack(side=tk.LEFT)
    
    def toggle_email_feature(self):
        """Toggle email feature controls"""
        enabled = self.email_enabled_var.get()

        # Enable/disable controls
        state = tk.NORMAL if enabled else tk.DISABLED
        self.test_button.configure(state=state)

        # Enable/disable email management
        self.new_email_entry.configure(state=state)
        self.add_email_button.configure(state=state)
        self.remove_email_button.configure(state=state)
        self.email_listbox.configure(state=state)
    
    def load_settings(self):
        """Load current settings from database"""
        # Load SMTP configuration
        smtp_config = self.email_manager.get_smtp_config()

        self.email_enabled_var.set(smtp_config['email_enabled'])

        # Load email addresses
        self.refresh_email_list()

        # Update UI state
        self.toggle_email_feature()
    
    def refresh_email_list(self):
        """Refresh the email addresses list"""
        self.email_listbox.delete(0, tk.END)
        emails = self.email_manager.get_email_addresses()
        for email in emails:
            self.email_listbox.insert(tk.END, email)
    
    def add_email(self):
        """Add new email address"""
        email = self.new_email_var.get().strip()
        
        if not email:
            messagebox.showwarning("Warning", "Please enter an email address")
            return
        
        if not self.email_manager.validate_email(email):
            messagebox.showerror("Error", "Invalid email address format")
            return
        
        success, message = self.email_manager.add_email_address(email)
        
        if success:
            self.new_email_var.set("")
            self.refresh_email_list()
            messagebox.showinfo("Success", message)
        else:
            messagebox.showerror("Error", message)
    
    def remove_email(self):
        """Remove selected email address"""
        selection = self.email_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an email address to remove")
            return
        
        email = self.email_listbox.get(selection[0])
        
        if messagebox.askyesno("Confirm", f"Remove email address: {email}?"):
            if self.email_manager.remove_email_address(email):
                self.refresh_email_list()
                messagebox.showinfo("Success", "Email address removed")
            else:
                messagebox.showerror("Error", "Failed to remove email address")
    
    def test_connection(self):
        """Test SMTP connection"""
        # Test in separate thread to avoid UI freezing
        def test_thread():
            success, message = self.email_manager.test_smtp_connection()
            self.window.after(0, lambda: self.show_test_result(success, message))

        threading.Thread(target=test_thread, daemon=True).start()
        messagebox.showinfo("Testing", "Testing connection... Please wait.")
    
    def show_test_result(self, success, message):
        """Show test connection result"""
        if success:
            messagebox.showinfo("Success", message)
        else:
            messagebox.showerror("Error", message)
    
    def save_settings(self):
        """Save all settings"""
        # Save email enabled status
        if self.email_manager.save_smtp_config(self.email_enabled_var.get()):
            messagebox.showinfo("Success", "Settings saved successfully")
            self.close_window()
        else:
            messagebox.showerror("Error", "Failed to save settings")
    
    def close_window(self):
        """Close the settings window"""
        self.window.destroy()
