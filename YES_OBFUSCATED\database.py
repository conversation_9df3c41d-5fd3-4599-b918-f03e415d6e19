"""
Database operations for POS System
Handles all database connections and operations
"""

import sqlite3
import hashlib
import os
from datetime import datetime
from contextlib import contextmanager

def get_db_connection():
    """Get database connection with row factory"""
    conn = sqlite3.connect('pos_system.db')
    conn.row_factory = sqlite3.Row  # Enable column access by name
    return conn

@contextmanager
def safe_db_connection():
    """Context manager for safe database connections with guaranteed cleanup"""
    conn = None
    try:
        conn = get_db_connection()
        yield conn
    except Exception as e:
        if conn:
            try:
                conn.rollback()
            except:
                pass  # Rollback might fail if connection is broken
        raise e
    finally:
        if conn:
            try:
                conn.close()
            except:
                pass  # Close might fail if connection is already closed

def init_database():
    """Initialize database with all required tables"""
    conn = get_db_connection()
    try:
        c = conn.cursor()

        # Users table
        c.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'user',
                is_admin INTEGER DEFAULT 0,
                button_color TEXT DEFAULT '#f8f9fa',
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Categories table
        c.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                image BLOB,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Products table
        c.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                category_id INTEGER,
                price REAL NOT NULL,
                image BLOB,
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        ''')

        # Sales table
        c.execute('''
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                total REAL NOT NULL,
                date DATETIME DEFAULT CURRENT_TIMESTAMP,
                items TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Add items column if it doesn't exist (for existing databases)
        try:
            c.execute("ALTER TABLE sales ADD COLUMN items TEXT")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Sale items table
        c.execute('''
            CREATE TABLE IF NOT EXISTS sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER,
                product_name TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                price REAL NOT NULL,
                is_extra INTEGER DEFAULT 0,
                FOREIGN KEY (sale_id) REFERENCES sales (id)
            )
        ''')

        # Receipt settings table
        c.execute('''
            CREATE TABLE IF NOT EXISTS receipt_settings (
                id INTEGER PRIMARY KEY,
                business_name TEXT DEFAULT 'Your Business Name',
                business_address TEXT DEFAULT 'Your Business Address',
                business_phone TEXT DEFAULT 'Your Phone Number',
                header_text TEXT DEFAULT 'POS SYSTEM RECEIPT',
                footer_text TEXT DEFAULT 'Thank you for your business!',
                logo_image BLOB,
                paper_size TEXT DEFAULT '300x95',
                font_size INTEGER DEFAULT 19,
                line_spacing INTEGER DEFAULT 8,
                selected_printer TEXT DEFAULT 'Default Printer'
            )
        ''')

        # Add line_spacing column if it doesn't exist (for existing databases)
        try:
            c.execute("ALTER TABLE receipt_settings ADD COLUMN line_spacing INTEGER DEFAULT 8")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Add selected_printer column if it doesn't exist (for existing databases)
        try:
            c.execute("ALTER TABLE receipt_settings ADD COLUMN selected_printer TEXT DEFAULT 'Default Printer'")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Add logo size column if it doesn't exist (for existing databases)
        try:
            c.execute("ALTER TABLE receipt_settings ADD COLUMN logo_size INTEGER DEFAULT 100")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Add logo file path column if it doesn't exist (for existing databases)
        try:
            c.execute("ALTER TABLE receipt_settings ADD COLUMN logo_file_path TEXT")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Custom paper sizes table
        c.execute('''
            CREATE TABLE IF NOT EXISTS custom_paper_sizes (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                width INTEGER NOT NULL,
                height INTEGER NOT NULL
            )
        ''')

        # System settings table
        c.execute('''
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY,
                language TEXT DEFAULT 'english',
                history_reset_hour INTEGER DEFAULT 6,
                history_reset_minute INTEGER DEFAULT 0,
                max_product_columns INTEGER DEFAULT 4,
                product_button_size INTEGER DEFAULT 130,
                updated_date DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Add history reset time columns if they don't exist (for existing databases)
        try:
            c.execute("ALTER TABLE system_settings ADD COLUMN history_reset_hour INTEGER DEFAULT 6")
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            c.execute("ALTER TABLE system_settings ADD COLUMN history_reset_minute INTEGER DEFAULT 0")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Add display settings columns if they don't exist (for existing databases)
        try:
            c.execute("ALTER TABLE system_settings ADD COLUMN max_product_columns INTEGER DEFAULT 4")
        except sqlite3.OperationalError:
            pass  # Column already exists

        try:
            c.execute("ALTER TABLE system_settings ADD COLUMN product_button_size INTEGER DEFAULT 130")
        except sqlite3.OperationalError:
            pass  # Column already exists

        # Create default admin user if none exists (but don't update existing admin password)
        c.execute("SELECT COUNT(*) FROM users WHERE username='admin'")
        if c.fetchone()[0] == 0:
            hashed_password = hashlib.sha256('@H@W@LeComptoir@'.encode()).hexdigest()
            c.execute("""
                INSERT INTO users (username, password, role, is_admin, button_color)
                VALUES (?, ?, ?, ?, ?)
            """, ('admin', hashed_password, 'admin', 1, '#007bff'))
            print("Default admin user created with password: @H@W@LeComptoir@")
        # Note: If admin user already exists, we preserve their current password

        # No default categories or products - clean start

        # Create default receipt settings if none exist
        c.execute("SELECT COUNT(*) FROM receipt_settings WHERE id=1")
        if c.fetchone()[0] == 0:
            c.execute("""
                INSERT INTO receipt_settings (id) VALUES (1)
            """)

        # Create default system settings if none exist
        c.execute("SELECT COUNT(*) FROM system_settings WHERE id=1")
        if c.fetchone()[0] == 0:
            c.execute("""
                INSERT INTO system_settings (id, language) VALUES (1, 'english')
            """)

        # Storage/inventory management tables
        c.execute('''
            CREATE TABLE IF NOT EXISTS storage (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                current_stock INTEGER NOT NULL DEFAULT 0,
                min_stock_level INTEGER NOT NULL DEFAULT 5,
                max_stock_level INTEGER NOT NULL DEFAULT 100,
                unit TEXT NOT NULL DEFAULT 'pieces',
                cost_per_unit REAL NOT NULL DEFAULT 0.0,
                supplier TEXT DEFAULT '',
                last_restocked TEXT DEFAULT '',
                notes TEXT DEFAULT '',
                FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
            )
        ''')

        # Stock movements table for tracking inventory changes
        c.execute('''
            CREATE TABLE IF NOT EXISTS stock_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                reason TEXT NOT NULL,
                date TEXT NOT NULL,
                user TEXT NOT NULL,
                cost_per_unit REAL DEFAULT 0.0,
                FOREIGN KEY (product_id) REFERENCES products (id) ON DELETE CASCADE
            )
        ''')

        # Email settings table for email recipients
        c.execute('''
            CREATE TABLE IF NOT EXISTS email_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email_address TEXT UNIQUE NOT NULL,
                is_active INTEGER DEFAULT 1,
                added_date TEXT,
                added_by_user_id INTEGER,
                FOREIGN KEY (added_by_user_id) REFERENCES users (id)
            )
        ''')

        # SMTP configuration table for email functionality
        c.execute('''
            CREATE TABLE IF NOT EXISTS smtp_config (
                id INTEGER PRIMARY KEY,
                smtp_server TEXT DEFAULT 'smtp.gmail.com',
                smtp_port INTEGER DEFAULT 587,
                sender_email TEXT,
                sender_password TEXT,
                email_enabled INTEGER DEFAULT 0
            )
        ''')

        conn.commit()
        print("Database initialized successfully!")

    except Exception as e:
        print(f"Error initializing database: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def authenticate_user(username, password):
    """Authenticate user credentials"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        hashed_password = hashlib.sha256(password.encode()).hexdigest()
        c.execute("""
            SELECT id, username, role, is_admin, button_color
            FROM users
            WHERE username = ? AND password = ?
        """, (username, hashed_password))

        user = c.fetchone()
        if user:
            return dict(user)
        return None
    finally:
        conn.close()

def get_available_users():
    """Get list of all users for login dropdown"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("SELECT username, button_color FROM users ORDER BY username")
        return [dict(row) for row in c.fetchall()]
    finally:
        conn.close()

def save_language_setting(language):
    """Save language preference"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("""
            UPDATE system_settings
            SET language = ?, updated_date = CURRENT_TIMESTAMP
            WHERE id = 1
        """, (language,))
        conn.commit()
    finally:
        conn.close()

def load_language_setting():
    """Load saved language preference"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("SELECT language FROM system_settings WHERE id = 1")
        result = c.fetchone()
        return result['language'] if result else 'english'
    finally:
        conn.close()

def save_history_reset_time(hour, minute):
    """Save history reset time preference"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("""
            UPDATE system_settings
            SET history_reset_hour = ?, history_reset_minute = ?, updated_date = CURRENT_TIMESTAMP
            WHERE id = 1
        """, (hour, minute))
        conn.commit()
        print(f"History reset time saved: {hour:02d}:{minute:02d}")
    except Exception as e:
        print(f"Error saving history reset time: {e}")
    finally:
        conn.close()

def load_history_reset_time():
    """Load saved history reset time preference"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("SELECT history_reset_hour, history_reset_minute FROM system_settings WHERE id = 1")
        result = c.fetchone()
        if result:
            return result['history_reset_hour'], result['history_reset_minute']
        else:
            return 6, 0  # Default: 6:00 AM
    except Exception as e:
        print(f"Error loading history reset time: {e}")
        return 6, 0  # Default: 6:00 AM
    finally:
        conn.close()

def get_display_settings():
    """Get display settings from database"""
    conn = get_db_connection()
    try:
        c = conn.cursor()
        c.execute("SELECT max_product_columns, product_button_size FROM system_settings WHERE id = 1")
        result = c.fetchone()

        if result:
            return {
                'max_columns': result['max_product_columns'] or 4,
                'button_size': result['product_button_size'] or 130
            }
        else:
            # Return defaults if no settings found
            return {
                'max_columns': 4,
                'button_size': 130
            }
    except Exception as e:
        print(f"Error getting display settings: {e}")
        return {
            'max_columns': 4,
            'button_size': 130
        }
    finally:
        conn.close()

def save_display_settings(max_columns, button_size):
    """Save display settings to database"""
    conn = get_db_connection()
    try:
        c = conn.cursor()

        # Check if settings exist
        c.execute("SELECT id FROM system_settings WHERE id = 1")
        if c.fetchone():
            # Update existing
            c.execute("""
                UPDATE system_settings
                SET max_product_columns = ?, product_button_size = ?, updated_date = CURRENT_TIMESTAMP
                WHERE id = 1
            """, (max_columns, button_size))
        else:
            # Insert new
            c.execute("""
                INSERT INTO system_settings (id, max_product_columns, product_button_size)
                VALUES (1, ?, ?)
            """, (max_columns, button_size))

        conn.commit()
        print(f"Display settings saved: {max_columns} columns, {button_size}px buttons")
        return True
    except Exception as e:
        print(f"Error saving display settings: {e}")
        return False
    finally:
        conn.close()


