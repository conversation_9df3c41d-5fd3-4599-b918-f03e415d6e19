# PROTECTED CLIENT VERSION - OBFUSCATED CODE
# This file is part of a licensed POS system
# Unauthorized modification or redistribution is prohibited
# Contact your administrator for support

import base64
import zlib
import sys
from types import ModuleType

# Obfuscated code data
_PROTECTED_CODE = """eJyNVd9r2zAQfvdfcXMfao/OKYy9FPJQ2jIKbVOWwBgMhGKfZxFF8iS5axj733eSf8RJk2V+sS3dffru7rvT2btJY81kKdQE1QvUG1dp9TGK4zh6ns1hvrEO1/DIhYIH3qi8QhMtKmGhFBJBtksWXIWw9kbeide1FDl3QqsAFIl1rY0Du7H9px6+nOE5Lnm+iqIzuC4KyBtjUDkohMHcabMBp6HmroJSG2i9bERYmV/MhLJoXHJ5QZjtCjkqvsak/+dL698JY54zY2maRlGBJeQGuUNWoF05XTNbEXLeOCZKphALLJL0KgJ6KIibYAudLfS2IEpQWiHgq7DO+mi9gzOb1tM/ZzBTcuPXQCv4KlShf9lhlwCIqCcM76Zwrtz51tM/Bl1jVDSslUavQzqkWHbZgGf6jUbn3VSYrzzyQJNL4l9sOpqDaR/ONEBklaa0pTCB+LbdiMc0lXa9Q9bi9Pk5CTdTeGvEC8YHsf8b/1A+hqoFiUwHDnTQA8KNXtdOC5NJtdoeuOQur044kE08zun90WxegF2JepypHU5DLEDy3Z58JMT98OjgtsZcFWAatSUR1OtbbEcZRzTdK+XI9oBxZD9JW0b4mmPt4C686GzgFnCs9Futzh2UXMgwEmgS7Miw5xwstjKsjVAuKeMn7fCKStDIIqghP9x0V/Ab/8RdE/upM+rTMKlofFCz1ZpQw9DwVLbT7GCTthTiaQzv4dNlur/uPwPCt/ni7hHmi+svi/unz/EbwwFg3I7/mB1hzkBCM+TDUup8JdSP9FQ1xgPqiFKs4362dnGPB/KOWmptWahRN0dm8+uR6WDrTaZ7u0k63s5ImnsiadncGUMF2JVJX+6ObzDpa7qXzmeJ3NJ0VbYxJCcpqUF+NnQzFLDWRSPp6uF+44X0xJcSsxHEcLNkAYwRr470CRn3/FruIZVUlpGC9skeP6kUiki/1dl3NbpdbdU4uhMU5DR8JLoQRUTqYMzfC4zBdAoxY17qjMUtWKv76C/MXF8d"""

def _load_protected_module():
    """Load the protected module code"""
    try:
        # Hardware lock enforcement - MUST be first
        try:
            from hardware_lock import enforce_hardware_lock
            if not enforce_hardware_lock():
                sys.exit(1)
        except ImportError:
            print("SECURITY ERROR: Security module missing. System cannot start.")
            sys.exit(1)
        except Exception:
            print("SECURITY ERROR: Security check failed. System cannot start.")
            sys.exit(1)

        # Decode and decompress the protected code
        compressed_data = base64.b64decode(_PROTECTED_CODE.encode('ascii'))
        source_code = zlib.decompress(compressed_data).decode('utf-8')

        # Execute the code in the current module's namespace
        exec(source_code, globals())

    except Exception as e:
        print(f"Error loading protected module: {e}")
        raise ImportError("Failed to load protected module")

# Load the protected code when this module is imported
_load_protected_module()
