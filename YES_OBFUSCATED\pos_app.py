# PROTECTED CLIENT VERSION - OBFUSCATED CODE
# This file is part of a licensed POS system
# Unauthorized modification or redistribution is prohibited
# Contact your administrator for support

import base64
import zlib
import sys
from types import ModuleType

# Obfuscated code data
_PROTECTED_CODE = """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"""

def _load_protected_module():
    """Load the protected module code"""
    try:
        # Decode and decompress the protected code
        compressed_data = base64.b64decode(_PROTECTED_CODE.encode('ascii'))
        source_code = zlib.decompress(compressed_data).decode('utf-8')
        
        # Execute the code in the current module's namespace
        exec(source_code, globals())
        
    except Exception as e:
        print(f"Error loading protected module: {e}")
        raise ImportError("Failed to load protected module")

# Load the protected code when this module is imported
_load_protected_module()
