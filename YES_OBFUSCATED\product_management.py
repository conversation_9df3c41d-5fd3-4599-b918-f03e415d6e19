# PROTECTED CLIENT VERSION - OBFUSCATED CODE
# This file is part of a licensed POS system
# Unauthorized modification or redistribution is prohibited
# Contact your administrator for support

import base64
import zlib
import sys
from types import ModuleType

# Obfuscated code data
_PROTECTED_CODE = """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"""

def _load_protected_module():
    """Load the protected module code"""
    try:
        # Decode and decompress the protected code
        compressed_data = base64.b64decode(_PROTECTED_CODE.encode('ascii'))
        source_code = zlib.decompress(compressed_data).decode('utf-8')
        
        # Execute the code in the current module's namespace
        exec(source_code, globals())
        
    except Exception as e:
        print(f"Error loading protected module: {e}")
        raise ImportError("Failed to load protected module")

# Load the protected code when this module is imported
_load_protected_module()
