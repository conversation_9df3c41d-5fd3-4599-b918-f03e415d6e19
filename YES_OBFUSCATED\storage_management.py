# PROTECTED CLIENT VERSION - OBFUSCATED CODE
# This file is part of a licensed POS system
# Unauthorized modification or redistribution is prohibited
# Contact your administrator for support

import base64
import zlib
import sys
from types import ModuleType

# Obfuscated code data
_PROTECTED_CODE = """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"""

def _load_protected_module():
    """Load the protected module code"""
    try:
        # Decode and decompress the protected code
        compressed_data = base64.b64decode(_PROTECTED_CODE.encode('ascii'))
        source_code = zlib.decompress(compressed_data).decode('utf-8')
        
        # Execute the code in the current module's namespace
        exec(source_code, globals())
        
    except Exception as e:
        print(f"Error loading protected module: {e}")
        raise ImportError("Failed to load protected module")

# Load the protected code when this module is imported
_load_protected_module()
