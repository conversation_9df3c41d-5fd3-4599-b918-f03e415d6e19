# PROTECTED CLIENT VERSION - OBFUSCATED CODE
# This file is part of a licensed POS system
# Unauthorized modification or redistribution is prohibited
# Contact your administrator for support

import base64
import zlib
import sys
from types import ModuleType

# Obfuscated code data
_PROTECTED_CODE = """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"""

def _load_protected_module():
    """Load the protected module code"""
    try:
        # Decode and decompress the protected code
        compressed_data = base64.b64decode(_PROTECTED_CODE.encode('ascii'))
        source_code = zlib.decompress(compressed_data).decode('utf-8')
        
        # Execute the code in the current module's namespace
        exec(source_code, globals())
        
    except Exception as e:
        print(f"Error loading protected module: {e}")
        raise ImportError("Failed to load protected module")

# Load the protected code when this module is imported
_load_protected_module()
