# PROTECTED CLIENT VERSION - OBFUSCATED CODE
# This file is part of a licensed POS system
# Unauthorized modification or redistribution is prohibited
# Contact your administrator for support

import base64
import zlib
import sys
from types import ModuleType

# Obfuscated code data
_PROTECTED_CODE = """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"""

def _load_protected_module():
    """Load the protected module code"""
    try:
        # Decode and decompress the protected code
        compressed_data = base64.b64decode(_PROTECTED_CODE.encode('ascii'))
        source_code = zlib.decompress(compressed_data).decode('utf-8')
        
        # Execute the code in the current module's namespace
        exec(source_code, globals())
        
    except Exception as e:
        print(f"Error loading protected module: {e}")
        raise ImportError("Failed to load protected module")

# Load the protected code when this module is imported
_load_protected_module()
