#!/usr/bin/env python3
"""
Check sales data in database
"""

def check_sales():
    print("=== CHECKING SALES DATA ===")
    
    try:
        from database import safe_db_connection
        
        with safe_db_connection() as conn:
            c = conn.cursor()
            
            # Check if sales table exists
            c.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='sales'")
            table_exists = c.fetchone()
            
            if table_exists:
                print("✅ Sales table exists")
                
                # Count sales
                c.execute("SELECT COUNT(*) FROM sales")
                count = c.fetchone()[0]
                print(f"Sales records: {count}")
                
                if count == 0:
                    print("No sales data found. Creating sample data...")
                    
                    # Create sample sales
                    from datetime import datetime, timedelta
                    
                    sample_sales = [
                        ('admin', 25.50, 'Coffee, Croissant', datetime.now() - timedelta(hours=2)),
                        ('cashier', 18.75, 'Tea, Sandwich', datetime.now() - timedelta(hours=1)),
                        ('admin', 32.25, 'Lunch Menu', datetime.now() - timedelta(minutes=30))
                    ]
                    
                    for username, total, items, timestamp in sample_sales:
                        c.execute("""
                            INSERT INTO sales (username, total, items, timestamp)
                            VALUES (?, ?, ?, ?)
                        """, (username, total, items, timestamp))
                    
                    conn.commit()
                    print(f"✅ Created {len(sample_sales)} sample sales")
                
                # Show sample data
                c.execute("SELECT * FROM sales ORDER BY timestamp DESC LIMIT 5")
                sales = c.fetchall()
                
                print("\nSample sales data:")
                for sale in sales:
                    print(f"  ID: {sale[0]}, User: {sale[1]}, Total: {sale[2]:.2f}, Items: {sale[3]}")
                
                return True
            else:
                print("❌ Sales table does not exist")
                return False
                
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_receipt_generation():
    print("\n=== TESTING RECEIPT GENERATION ===")
    
    try:
        # Simple receipt content
        content = """SALES HISTORY REPORT
==================================================
Period: All Time
Cashier: All Users
Generated: 13/06/2025 19:00:00

Date/Time           User        Total
--------------------------------------------------
13/06/2025 17:00:00 admin       25.50 MAD
13/06/2025 18:00:00 cashier     18.75 MAD
13/06/2025 18:30:00 admin       32.25 MAD
--------------------------------------------------
TOTAL: 76.50 MAD

Report contains 3 transaction(s)"""
        
        print("✅ Sample receipt content:")
        print(content)
        
        # Test PDF generation
        from pdf_generator import PDFGenerator
        pdf_gen = PDFGenerator()
        
        business_info = {
            'business_name': 'Le Comptoir POS',
            'business_address': '123 Business Street',
            'business_phone': '+1234567890'
        }
        
        pdf_path = pdf_gen.create_history_pdf(content, business_info)
        
        if pdf_path:
            print(f"✅ PDF generated: {pdf_path}")
            
            # Test email
            from email_manager import EmailManager
            em = EmailManager()
            
            # Enable email and add test address
            em.save_smtp_config(True)
            em.add_email_address("<EMAIL>")
            
            subject = "Sales History Report - Test"
            body = "This is a test email with sales data."
            
            success, message = em.send_history_email(pdf_path, subject, body)
            print(f"Email test: {success}")
            print(f"Message: {message}")
            
            # Cleanup
            pdf_gen.cleanup_temp_file(pdf_path)
            em.remove_email_address("<EMAIL>")
            
            return success
        else:
            print("❌ PDF generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Receipt generation failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 SALES DATA CHECK")
    print("=" * 30)
    
    # Check sales data
    sales_ok = check_sales()
    
    # Test receipt generation
    receipt_ok = test_receipt_generation()
    
    print("\n" + "=" * 30)
    print("📊 RESULTS")
    print("=" * 30)
    print(f"Sales Data: {'✅ OK' if sales_ok else '❌ FAILED'}")
    print(f"Receipt Generation: {'✅ OK' if receipt_ok else '❌ FAILED'}")
    
    if sales_ok and receipt_ok:
        print("\n🎉 SALES DATA ISSUE FIXED!")
        print("The POS system should now work correctly.")
    else:
        print("\n❌ ISSUES REMAIN")
        print("Check the output above for details.")
