#!/usr/bin/env python3
"""
Check the actual structure of the sales table
"""

def check_table_structure():
    print("=== CHECKING TABLE STRUCTURE ===")
    
    try:
        from database import safe_db_connection
        
        with safe_db_connection() as conn:
            c = conn.cursor()
            
            # Get table info
            c.execute("PRAGMA table_info(sales)")
            columns = c.fetchall()
            
            print("Sales table columns:")
            for col in columns:
                print(f"  {col[1]} ({col[2]}) - {col}")
            
            # Get sample data
            c.execute("SELECT * FROM sales LIMIT 3")
            sales = c.fetchall()
            
            print(f"\nSample data ({len(sales)} records):")
            for sale in sales:
                print(f"  {sale}")
            
            # Try different column names
            possible_time_columns = ['timestamp', 'date', 'created_at', 'sale_date', 'time']
            
            for col_name in possible_time_columns:
                try:
                    c.execute(f"SELECT {col_name} FROM sales LIMIT 1")
                    result = c.fetchone()
                    print(f"✅ Column '{col_name}' exists: {result}")
                    break
                except Exception as e:
                    print(f"❌ Column '{col_name}' not found: {e}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    check_table_structure()
