import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from email_manager import EmailManager
import threading

class EmailSettingsWindow:
    def __init__(self, parent, app):
        self.parent = parent
        self.app = app
        self.email_manager = EmailManager()
        
        # Create popup window
        self.window = tk.Toplevel(parent)
        self.window.title(self.app.get_text('email_settings'))
        self.window.geometry("600x500")
        self.window.configure(bg='#2b2b2b')
        self.window.resizable(False, False)
        
        # Make window modal
        self.window.transient(parent)
        self.window.grab_set()
        
        # Center window
        self.center_window()
        
        # Apply theme
        self.apply_theme()
        
        # Create UI
        self.create_widgets()
        
        # Load current settings
        self.load_settings()
        
        # Focus window
        self.window.focus_set()
    
    def center_window(self):
        """Center the window on screen"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.window.winfo_screenheight() // 2) - (500 // 2)
        self.window.geometry(f"600x500+{x}+{y}")
    
    def apply_theme(self):
        """Apply dark theme to window"""
        style = ttk.Style()
        
        # Configure styles for dark theme
        style.configure('Dark.TFrame', background='#2b2b2b')
        style.configure('Dark.TLabel', background='#2b2b2b', foreground='white', font=('Segoe UI', 10))
        style.configure('Dark.TButton', background='#ff8000', foreground='white', font=('Segoe UI', 10))
        style.configure('Dark.TEntry', fieldbackground='#404040', foreground='white', font=('Segoe UI', 10))
        style.configure('Dark.TCheckbutton', background='#2b2b2b', foreground='white', font=('Segoe UI', 10))
        style.configure('Dark.TCombobox', fieldbackground='#404040', foreground='white', font=('Segoe UI', 10))
        
        # Configure Listbox colors
        self.window.option_add('*TCombobox*Listbox.Background', '#404040')
        self.window.option_add('*TCombobox*Listbox.Foreground', 'white')
    
    def create_widgets(self):
        """Create all UI widgets"""
        # Main container
        main_frame = ttk.Frame(self.window, style='Dark.TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Title
        title_label = ttk.Label(main_frame, text=self.app.get_text('email_settings'), 
                               style='Dark.TLabel', font=('Segoe UI', 16, 'bold'))
        title_label.pack(pady=(0, 20))
        
        # Email Configuration Section
        config_frame = ttk.LabelFrame(main_frame, text="SMTP Configuration", style='Dark.TFrame')
        config_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Enable email checkbox
        self.email_enabled_var = tk.BooleanVar()
        self.email_enabled_cb = ttk.Checkbutton(config_frame, text="Enable Email Feature", 
                                               variable=self.email_enabled_var,
                                               style='Dark.TCheckbutton',
                                               command=self.toggle_email_feature)
        self.email_enabled_cb.pack(anchor=tk.W, padx=10, pady=5)
        
        # SMTP Server
        smtp_frame = ttk.Frame(config_frame, style='Dark.TFrame')
        smtp_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(smtp_frame, text="SMTP Server:", style='Dark.TLabel').pack(side=tk.LEFT)
        self.smtp_server_var = tk.StringVar(value="smtp.gmail.com")
        self.smtp_server_entry = ttk.Entry(smtp_frame, textvariable=self.smtp_server_var, 
                                          style='Dark.TEntry', width=30)
        self.smtp_server_entry.pack(side=tk.RIGHT)
        
        # SMTP Port
        port_frame = ttk.Frame(config_frame, style='Dark.TFrame')
        port_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(port_frame, text="SMTP Port:", style='Dark.TLabel').pack(side=tk.LEFT)
        self.smtp_port_var = tk.StringVar(value="587")
        self.smtp_port_entry = ttk.Entry(port_frame, textvariable=self.smtp_port_var, 
                                        style='Dark.TEntry', width=10)
        self.smtp_port_entry.pack(side=tk.RIGHT)
        
        # Sender Email
        sender_frame = ttk.Frame(config_frame, style='Dark.TFrame')
        sender_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(sender_frame, text="Sender Email:", style='Dark.TLabel').pack(side=tk.LEFT)
        self.sender_email_var = tk.StringVar()
        self.sender_email_entry = ttk.Entry(sender_frame, textvariable=self.sender_email_var, 
                                           style='Dark.TEntry', width=30)
        self.sender_email_entry.pack(side=tk.RIGHT)
        
        # Sender Password
        password_frame = ttk.Frame(config_frame, style='Dark.TFrame')
        password_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(password_frame, text="App Password:", style='Dark.TLabel').pack(side=tk.LEFT)
        self.sender_password_var = tk.StringVar()
        self.sender_password_entry = ttk.Entry(password_frame, textvariable=self.sender_password_var, 
                                              style='Dark.TEntry', width=30, show="*")
        self.sender_password_entry.pack(side=tk.RIGHT)
        
        # Test connection button
        self.test_button = ttk.Button(config_frame, text="Test Connection", 
                                     style='Dark.TButton', command=self.test_connection)
        self.test_button.pack(pady=10)
        
        # Email Addresses Section
        addresses_frame = ttk.LabelFrame(main_frame, text="Email Recipients", style='Dark.TFrame')
        addresses_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # Add email section
        add_frame = ttk.Frame(addresses_frame, style='Dark.TFrame')
        add_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(add_frame, text="Add Email:", style='Dark.TLabel').pack(side=tk.LEFT)
        self.new_email_var = tk.StringVar()
        self.new_email_entry = ttk.Entry(add_frame, textvariable=self.new_email_var, 
                                        style='Dark.TEntry', width=30)
        self.new_email_entry.pack(side=tk.LEFT, padx=(10, 5))
        self.new_email_entry.bind('<Return>', lambda e: self.add_email())
        
        self.add_email_button = ttk.Button(add_frame, text="Add", style='Dark.TButton', 
                                          command=self.add_email)
        self.add_email_button.pack(side=tk.LEFT)
        
        # Email list
        list_frame = ttk.Frame(addresses_frame, style='Dark.TFrame')
        list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Create listbox with scrollbar
        self.email_listbox = tk.Listbox(list_frame, bg='#404040', fg='white', 
                                       font=('Segoe UI', 10), selectbackground='#ff8000')
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.email_listbox.yview)
        self.email_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.email_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Remove email button
        self.remove_email_button = ttk.Button(addresses_frame, text="Remove Selected", 
                                             style='Dark.TButton', command=self.remove_email)
        self.remove_email_button.pack(pady=5)
        
        # Bottom buttons
        button_frame = ttk.Frame(main_frame, style='Dark.TFrame')
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        self.save_button = ttk.Button(button_frame, text="Save Settings", 
                                     style='Dark.TButton', command=self.save_settings)
        self.save_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.cancel_button = ttk.Button(button_frame, text="Cancel", 
                                       style='Dark.TButton', command=self.close_window)
        self.cancel_button.pack(side=tk.LEFT)
    
    def toggle_email_feature(self):
        """Toggle email feature controls"""
        enabled = self.email_enabled_var.get()
        
        # Enable/disable SMTP configuration
        state = tk.NORMAL if enabled else tk.DISABLED
        self.smtp_server_entry.configure(state=state)
        self.smtp_port_entry.configure(state=state)
        self.sender_email_entry.configure(state=state)
        self.sender_password_entry.configure(state=state)
        self.test_button.configure(state=state)
        
        # Enable/disable email management
        self.new_email_entry.configure(state=state)
        self.add_email_button.configure(state=state)
        self.remove_email_button.configure(state=state)
        self.email_listbox.configure(state=state)
    
    def load_settings(self):
        """Load current settings from database"""
        # Load SMTP configuration
        smtp_config = self.email_manager.get_smtp_config()
        
        self.email_enabled_var.set(smtp_config['email_enabled'])
        self.smtp_server_var.set(smtp_config['smtp_server'])
        self.smtp_port_var.set(str(smtp_config['smtp_port']))
        self.sender_email_var.set(smtp_config['sender_email'])
        self.sender_password_var.set(smtp_config['sender_password'])
        
        # Load email addresses
        self.refresh_email_list()
        
        # Update UI state
        self.toggle_email_feature()
    
    def refresh_email_list(self):
        """Refresh the email addresses list"""
        self.email_listbox.delete(0, tk.END)
        emails = self.email_manager.get_email_addresses()
        for email in emails:
            self.email_listbox.insert(tk.END, email)
    
    def add_email(self):
        """Add new email address"""
        email = self.new_email_var.get().strip()
        
        if not email:
            messagebox.showwarning("Warning", "Please enter an email address")
            return
        
        if not self.email_manager.validate_email(email):
            messagebox.showerror("Error", "Invalid email address format")
            return
        
        success, message = self.email_manager.add_email_address(email)
        
        if success:
            self.new_email_var.set("")
            self.refresh_email_list()
            messagebox.showinfo("Success", message)
        else:
            messagebox.showerror("Error", message)
    
    def remove_email(self):
        """Remove selected email address"""
        selection = self.email_listbox.curselection()
        if not selection:
            messagebox.showwarning("Warning", "Please select an email address to remove")
            return
        
        email = self.email_listbox.get(selection[0])
        
        if messagebox.askyesno("Confirm", f"Remove email address: {email}?"):
            if self.email_manager.remove_email_address(email):
                self.refresh_email_list()
                messagebox.showinfo("Success", "Email address removed")
            else:
                messagebox.showerror("Error", "Failed to remove email address")
    
    def test_connection(self):
        """Test SMTP connection"""
        config = {
            'smtp_server': self.smtp_server_var.get(),
            'smtp_port': int(self.smtp_port_var.get()) if self.smtp_port_var.get().isdigit() else 587,
            'sender_email': self.sender_email_var.get(),
            'sender_password': self.sender_password_var.get()
        }
        
        if not config['sender_email'] or not config['sender_password']:
            messagebox.showwarning("Warning", "Please enter sender email and password")
            return
        
        # Test in separate thread to avoid UI freezing
        def test_thread():
            success, message = self.email_manager.test_smtp_connection(config)
            self.window.after(0, lambda: self.show_test_result(success, message))
        
        threading.Thread(target=test_thread, daemon=True).start()
        messagebox.showinfo("Testing", "Testing connection... Please wait.")
    
    def show_test_result(self, success, message):
        """Show test connection result"""
        if success:
            messagebox.showinfo("Success", message)
        else:
            messagebox.showerror("Error", message)
    
    def save_settings(self):
        """Save all settings"""
        # Validate inputs
        if self.email_enabled_var.get():
            if not self.sender_email_var.get() or not self.sender_password_var.get():
                messagebox.showwarning("Warning", "Please enter sender email and password")
                return
            
            if not self.email_manager.validate_email(self.sender_email_var.get()):
                messagebox.showerror("Error", "Invalid sender email format")
                return
        
        # Save SMTP configuration
        config = {
            'smtp_server': self.smtp_server_var.get(),
            'smtp_port': int(self.smtp_port_var.get()) if self.smtp_port_var.get().isdigit() else 587,
            'sender_email': self.sender_email_var.get(),
            'sender_password': self.sender_password_var.get(),
            'email_enabled': self.email_enabled_var.get()
        }
        
        if self.email_manager.save_smtp_config(config):
            messagebox.showinfo("Success", "Settings saved successfully")
            self.close_window()
        else:
            messagebox.showerror("Error", "Failed to save settings")
    
    def close_window(self):
        """Close the settings window"""
        self.window.destroy()
