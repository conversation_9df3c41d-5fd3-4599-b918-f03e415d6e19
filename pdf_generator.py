from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.units import inch, mm
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
import os
import tempfile
from datetime import datetime
from io import BytesIO
import base64
from PIL import Image as PILImage

class PDFGenerator:
    def __init__(self):
        self.page_width = 210 * mm  # A4 width
        self.page_height = 297 * mm  # A4 height
        self.margin = 20 * mm
        
    def create_history_pdf(self, receipt_content, business_info=None, logo_data=None):
        """Create PDF from history receipt content"""
        try:
            # Create temporary PDF file
            temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
            pdf_path = temp_file.name
            temp_file.close()
            
            # Create PDF document
            doc = SimpleDocTemplate(
                pdf_path,
                pagesize=A4,
                rightMargin=self.margin,
                leftMargin=self.margin,
                topMargin=self.margin,
                bottomMargin=self.margin
            )
            
            # Build content
            story = []
            styles = getSampleStyleSheet()
            
            # Custom styles
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=16,
                spaceAfter=12,
                alignment=TA_CENTER,
                textColor=colors.black
            )
            
            header_style = ParagraphStyle(
                'CustomHeader',
                parent=styles['Normal'],
                fontSize=12,
                spaceAfter=6,
                alignment=TA_CENTER,
                textColor=colors.black
            )
            
            content_style = ParagraphStyle(
                'CustomContent',
                parent=styles['Normal'],
                fontSize=10,
                spaceAfter=3,
                alignment=TA_LEFT,
                fontName='Courier',  # Monospace font for receipt-like appearance
                textColor=colors.black
            )
            
            # Add logo if available
            if logo_data and business_info:
                try:
                    logo_image = self._process_logo_for_pdf(logo_data)
                    if logo_image:
                        story.append(logo_image)
                        story.append(Spacer(1, 12))
                except Exception as e:
                    print(f"Error adding logo to PDF: {e}")
            
            # Add business information
            if business_info:
                if business_info.get('business_name'):
                    story.append(Paragraph(business_info['business_name'].upper(), title_style))
                if business_info.get('business_address'):
                    story.append(Paragraph(business_info['business_address'], header_style))
                if business_info.get('business_phone'):
                    story.append(Paragraph(f"Tel: {business_info['business_phone']}", header_style))
                story.append(Spacer(1, 12))
            
            # Add generation timestamp
            timestamp = datetime.now().strftime('%d/%m/%Y %H:%M:%S')
            story.append(Paragraph(f"Generated: {timestamp}", header_style))
            story.append(Spacer(1, 12))
            
            # Process receipt content
            lines = receipt_content.split('\n')
            
            # Look for table content in history reports
            table_data = []
            in_table = False
            
            for line in lines:
                line = line.strip()
                if not line:
                    if table_data and in_table:
                        # End of table, create table
                        story.append(self._create_table(table_data))
                        story.append(Spacer(1, 12))
                        table_data = []
                        in_table = False
                    else:
                        story.append(Spacer(1, 6))
                    continue
                
                # Check if this looks like a table header
                if 'Produit:' in line and 'Prix' in line and 'Qté' in line:
                    in_table = True
                    # Parse table header
                    headers = ['Produit', 'Prix', 'Qté', 'Total']
                    table_data.append(headers)
                    continue
                
                # Check if this looks like table data
                if in_table and not line.startswith('_') and not line.startswith('='):
                    # Try to parse as table row
                    parts = line.split()
                    if len(parts) >= 3:
                        # This might be a table row
                        try:
                            # Try to identify price, quantity, total
                            product_name = parts[0]
                            price = parts[-3] if len(parts) >= 3 else ''
                            qty = parts[-2] if len(parts) >= 2 else ''
                            total = parts[-1] if len(parts) >= 1 else ''
                            table_data.append([product_name, price, qty, total])
                            continue
                        except:
                            pass
                
                # Regular content line
                if in_table and table_data:
                    # End table and add this line as regular content
                    story.append(self._create_table(table_data))
                    story.append(Spacer(1, 12))
                    table_data = []
                    in_table = False
                
                # Add as regular paragraph
                story.append(Paragraph(line, content_style))
            
            # Add any remaining table
            if table_data and in_table:
                story.append(self._create_table(table_data))
            
            # Add footer
            story.append(Spacer(1, 20))
            footer_style = ParagraphStyle(
                'Footer',
                parent=styles['Normal'],
                fontSize=8,
                alignment=TA_CENTER,
                textColor=colors.grey
            )
            story.append(Paragraph("Generated by POS System", footer_style))
            
            # Build PDF
            doc.build(story)
            
            return pdf_path
            
        except Exception as e:
            print(f"Error creating PDF: {e}")
            return None
    
    def _process_logo_for_pdf(self, logo_data):
        """Process logo data for PDF inclusion"""
        try:
            if logo_data.startswith('data:image'):
                # Extract base64 data
                base64_data = logo_data.split(',')[1]
                image_data = base64.b64decode(base64_data)
            else:
                # Assume it's a file path
                with open(logo_data, 'rb') as f:
                    image_data = f.read()
            
            # Create temporary image file
            temp_image = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            temp_image.write(image_data)
            temp_image.close()
            
            # Create reportlab Image
            img = Image(temp_image.name, width=100, height=50)  # Reasonable size
            
            # Clean up temp file
            try:
                os.unlink(temp_image.name)
            except:
                pass
            
            return img
            
        except Exception as e:
            print(f"Error processing logo: {e}")
            return None
    
    def _create_table(self, table_data):
        """Create a formatted table for the PDF"""
        if not table_data:
            return None
        
        try:
            # Create table
            table = Table(table_data, colWidths=[3*inch, 1*inch, 0.8*inch, 1*inch])
            
            # Style the table
            table.setStyle(TableStyle([
                # Header row
                ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('ALIGN', (1, 0), (-1, -1), 'RIGHT'),  # Right align numbers
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                
                # Data rows
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                
                # Alternating row colors
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
            ]))
            
            return table
            
        except Exception as e:
            print(f"Error creating table: {e}")
            return None
    
    def cleanup_temp_file(self, file_path):
        """Clean up temporary PDF file"""
        try:
            if file_path and os.path.exists(file_path):
                os.unlink(file_path)
        except Exception as e:
            print(f"Error cleaning up temp file: {e}")
