"""
Main POS Screen
Provides the main point-of-sale interface with categories, products, and cart
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import json

from database import get_db_connection
from number_keyboard import NumberKeyboard

class POSScreen:
    """Main POS interface screen"""

    def __init__(self, app):
        self.app = app
        self.root = app.root
        self.frame = None
        self.selected_category = None
        self.categories_frame = None
        self.products_frame = None
        self.cart_frame = None
        self.total_label = None
        self.clock_label = None

    def show(self):
        """Display the POS screen"""
        # Set new orange/black theme background
        self.root.configure(bg='#1a1a1a')

        # Create main frame
        self.frame = tk.Frame(self.root, bg='#1a1a1a')
        self.frame.pack(fill=tk.BOTH, expand=True)

        # Create POS interface
        self.create_pos_interface()

        # Start clock update
        self.update_clock()

    def hide(self):
        """Hide the POS screen"""
        if self.frame:
            self.frame.destroy()
            self.frame = None

    def refresh_language(self):
        """Refresh the screen with new language"""
        if self.frame:
            self.hide()
            self.show()

    def create_pos_interface(self):
        """Create the main POS interface"""
        # Top navigation bar
        self.create_navigation_bar()

        # Main content area
        content_frame = tk.Frame(self.frame, bg='#1a1a1a')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        # Configure grid weights with fixed proportions - OPTIMIZED CATEGORIES WIDTH
        content_frame.grid_rowconfigure(0, weight=1)
        content_frame.grid_columnconfigure(0, weight=0, minsize=204)  # 15% less than 240 (240 * 0.85 = 204)
        content_frame.grid_columnconfigure(1, weight=1, minsize=400)  # Flexible products area
        content_frame.grid_columnconfigure(2, weight=0, minsize=300)  # Fixed cart area

        # Left panel - Categories (smaller as per user preference)
        self.create_categories_panel(content_frame)

        # Center panel - Products (maximum 3 columns as per user preference)
        self.create_products_panel(content_frame)

        # Right panel - Cart and actions
        self.create_cart_panel(content_frame)

    def create_navigation_bar(self):
        """Create top navigation bar with clock and menu buttons"""
        nav_frame = tk.Frame(self.frame, bg='#2d2d2d', height=60)
        nav_frame.pack(fill=tk.X)
        nav_frame.pack_propagate(False)

        # Left side - User info and clock
        left_frame = tk.Frame(nav_frame, bg='#2d2d2d')
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=10)

        # User info
        user_label = tk.Label(left_frame,
                             text=f"{self.app.get_text('logged_in_as')} {self.app.current_user['username']}",
                             font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white')
        user_label.pack(side=tk.LEFT, pady=15)

        # Clock (hh:dd:ss --- dd/mm/yyyy format as per user preference)
        self.clock_label = tk.Label(left_frame, font=('Segoe UI', 12, 'bold'),
                                   bg='#2d2d2d', fg='white')
        self.clock_label.pack(side=tk.LEFT, padx=(20, 0), pady=15)

        # Right side - Menu buttons (only for admins)
        is_admin = self.app.current_user.get('is_admin', False) if isinstance(self.app.current_user, dict) else False
        if is_admin:
            self.create_admin_menu(nav_frame)

        # Right side buttons
        right_buttons_frame = tk.Frame(nav_frame, bg='#2d2d2d')
        right_buttons_frame.pack(side=tk.RIGHT, padx=10, pady=10)

        # Logout button (keep button color as requested)
        logout_btn = tk.Button(right_buttons_frame, text=self.app.get_text('logout'),
                              font=('Segoe UI', 10, 'bold'), bg='#dc3545', fg='white',
                              padx=15, pady=5, command=self.app.logout_user)
        logout_btn.pack(side=tk.RIGHT)

    def create_admin_menu(self, parent):
        """Create admin menu buttons"""
        menu_frame = tk.Frame(parent, bg='#2d2d2d')
        menu_frame.pack(side=tk.RIGHT, padx=10)

        # Admin menu buttons with icons
        admin_buttons = [
            ('user_management', self.app.get_text('users'), self.app.show_user_management),
            ('product_management', self.app.get_text('product_management'), self.app.show_product_management),
            ('sales_history', self.app.get_text('history'), self.app.show_sales_history),
            ('receipt_settings', self.app.get_text('receipts'), self.app.show_receipt_settings),
            ('storage_management', self.app.get_text('storage'), self.app.show_storage_management),
            ('display_settings', 'Display', self.show_display_settings),
        ]

        # Add license manager button only if available (full version)
        try:
            from license_database import LicenseManager
            admin_buttons.append(('license_manager', 'License Manager', self.app.show_license_manager))
        except ImportError:
            pass  # License manager not available in client version

        for i, (icon_key, text, command) in enumerate(admin_buttons):
            # Special handling for storage button to show low stock notification
            if icon_key == 'storage_management':
                # Check for low stock items
                try:
                    from storage_management import StorageManagement
                    low_stock_count = StorageManagement.get_low_stock_count()

                    if low_stock_count > 0:
                        # Add notification indicator to storage button text
                        text_with_notification = f"{text} 🔴({low_stock_count})"
                        button_color = '#ff6b6b'  # Red background for notification
                    else:
                        text_with_notification = text
                        button_color = None  # Default color
                except:
                    text_with_notification = text
                    button_color = None

                if self.app.icons.get(icon_key):
                    btn = tk.Button(menu_frame, image=self.app.icons[icon_key], text=text_with_notification,
                                   compound=tk.LEFT, font=('Segoe UI', 9),
                                   command=command, padx=8, pady=4)
                else:
                    btn = tk.Button(menu_frame, text=text_with_notification, font=('Segoe UI', 9),
                                   command=command, padx=12, pady=6)

                # Apply notification color if needed
                if button_color:
                    btn.configure(bg=button_color, fg='white')
            else:
                # Regular button creation for other admin buttons
                if self.app.icons.get(icon_key):
                    # Special handling for display settings button (icon only)
                    if icon_key == 'display_settings':
                        btn = tk.Button(menu_frame, image=self.app.icons[icon_key],
                                       command=command, padx=4, pady=4, width=40, height=40)
                    else:
                        btn = tk.Button(menu_frame, image=self.app.icons[icon_key], text=text,
                                       compound=tk.LEFT, font=('Segoe UI', 9),
                                       command=command, padx=8, pady=4)
                else:
                    btn = tk.Button(menu_frame, text=text, font=('Segoe UI', 9),
                                   command=command, padx=12, pady=6)

            btn.grid(row=0, column=i, padx=2)

    def create_categories_panel(self, parent):
        """Create categories selection panel"""
        # Categories frame with dark theme - optimized width
        cat_frame = tk.LabelFrame(parent, text=self.app.get_text('categories'),
                                 font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white', width=204)  # 15% less than 240
        cat_frame.grid(row=0, column=0, sticky='ns', padx=(0, 5))
        cat_frame.grid_propagate(False)  # Maintain fixed width

        # Scrollable categories - optimized canvas width
        cat_canvas = tk.Canvas(cat_frame, bg='#2d2d2d', width=184)  # 15% less than 220 (220 * 0.85 = 187, rounded to 184)
        cat_scrollbar = ttk.Scrollbar(cat_frame, orient="vertical", command=cat_canvas.yview)
        self.categories_frame = tk.Frame(cat_canvas, bg='#2d2d2d')

        cat_canvas.configure(yscrollcommand=cat_scrollbar.set)
        cat_canvas.pack(side="left", fill="both", expand=True)
        cat_scrollbar.pack(side="right", fill="y")

        cat_canvas.create_window((0, 0), window=self.categories_frame, anchor="nw")
        self.categories_frame.bind("<Configure>",
                                  lambda e: cat_canvas.configure(scrollregion=cat_canvas.bbox("all")))

        # Load categories
        self.load_categories()

    def create_products_panel(self, parent):
        """Create products display panel with responsive design"""
        # Products frame with dark theme - flexible but controlled
        prod_frame = tk.LabelFrame(parent, text=self.app.get_text('products'),
                                  font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white')
        prod_frame.grid(row=0, column=1, sticky='nsew', padx=5)

        # Calculate minimum height based on screen size
        screen_height = self.root.winfo_screenheight()
        min_height = max(400, int(screen_height * 0.6))  # At least 400px or 60% of screen height

        # Scrollable products with responsive height
        prod_canvas = tk.Canvas(prod_frame, bg='#2d2d2d', height=min_height)
        prod_scrollbar = ttk.Scrollbar(prod_frame, orient="vertical", command=prod_canvas.yview)
        self.products_frame = tk.Frame(prod_canvas, bg='#2d2d2d')

        prod_canvas.configure(yscrollcommand=prod_scrollbar.set)
        prod_canvas.pack(side="left", fill="both", expand=True)
        prod_scrollbar.pack(side="right", fill="y")

        prod_canvas.create_window((0, 0), window=self.products_frame, anchor="nw")
        self.products_frame.bind("<Configure>",
                                lambda e: prod_canvas.configure(scrollregion=prod_canvas.bbox("all")))

        # Enable mouse wheel scrolling
        def on_mousewheel(event):
            prod_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        prod_canvas.bind("<MouseWheel>", on_mousewheel)

        # Also bind mousewheel to the products frame itself
        self.products_frame.bind("<MouseWheel>", on_mousewheel)

        # Store canvas reference for potential future use
        self.products_canvas = prod_canvas

    def create_cart_panel(self, parent):
        """Create shopping cart and actions panel"""
        # Cart frame with dark theme - fixed width
        cart_main_frame = tk.LabelFrame(parent, text=self.app.get_text('shopping_cart'),
                                       font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white', width=300)
        cart_main_frame.grid(row=0, column=2, sticky='ns', padx=(5, 0))
        cart_main_frame.grid_propagate(False)  # Maintain fixed width

        # Scrollable cart items display
        cart_container = tk.Frame(cart_main_frame, bg='#2d2d2d')
        cart_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        cart_canvas = tk.Canvas(cart_container, bg='#2d2d2d', width=270)
        cart_scrollbar = ttk.Scrollbar(cart_container, orient="vertical", command=cart_canvas.yview)
        self.cart_frame = tk.Frame(cart_canvas, bg='#2d2d2d')

        cart_canvas.configure(yscrollcommand=cart_scrollbar.set)
        cart_canvas.pack(side="left", fill="both", expand=True)
        cart_scrollbar.pack(side="right", fill="y")

        cart_canvas.create_window((0, 0), window=self.cart_frame, anchor="nw")
        self.cart_frame.bind("<Configure>",
                            lambda e: cart_canvas.configure(scrollregion=cart_canvas.bbox("all")))

        # Enable mouse wheel scrolling for cart
        def on_cart_mousewheel(event):
            cart_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        cart_canvas.bind("<MouseWheel>", on_cart_mousewheel)

        # Total display (half-size text as per user preference)
        total_frame = tk.Frame(cart_main_frame, bg='#2d2d2d')
        total_frame.pack(fill=tk.X, padx=10, pady=5)

        self.total_label = tk.Label(total_frame, text=f"{self.app.get_text('total')} 0.00 MAD",
                                   font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='#ff8c00')
        self.total_label.pack()

        # Action buttons
        self.create_action_buttons(cart_main_frame)

    def create_action_buttons(self, parent):
        """Create cart action buttons"""
        actions_frame = tk.Frame(parent, bg='#2d2d2d')
        actions_frame.pack(fill=tk.X, padx=10, pady=10)

        # Button configurations with icons
        buttons = [
            ('checkout', self.app.get_text('checkout'), '#28a745', self.checkout),
            ('add_extra', self.app.get_text('add_extra'), '#007bff', self.add_extra_charge),
            ('remove_selected', self.app.get_text('remove'), '#ffc107', self.remove_selected),
            ('clear_cart', self.app.get_text('clear_cart'), '#dc3545', self.clear_cart),
        ]

        for i, (icon_key, text, color, command) in enumerate(buttons):
            # 1.5x larger buttons as per user preference (keep button colors)
            if self.app.icons.get(icon_key):
                btn = tk.Button(actions_frame, image=self.app.icons[icon_key], text=text,
                               compound=tk.TOP, font=('Segoe UI', 10, 'bold'),
                               bg=color, fg='white', width=120, height=60,
                               command=command)
            else:
                btn = tk.Button(actions_frame, text=text,
                               font=('Segoe UI', 10, 'bold'),
                               bg=color, fg='white', width=15, height=3,
                               command=command)
            btn.pack(fill=tk.X, pady=2)

    def load_categories(self):
        """Load categories from database"""
        # Clear existing categories
        for widget in self.categories_frame.winfo_children():
            widget.destroy()

        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("SELECT * FROM categories ORDER BY name")
            categories = c.fetchall()

            for i, category in enumerate(categories):
                # Create category button with image if available
                if category['image']:
                    try:
                        # Load image from database
                        from PIL import Image, ImageTk
                        import io

                        image_data = category['image']
                        image = Image.open(io.BytesIO(image_data))

                        # Resize image to be bigger but still clean (48x48 pixels)
                        image = image.resize((48, 48), Image.Resampling.LANCZOS)
                        photo = ImageTk.PhotoImage(image)

                        # Category button with larger image above text - OPTIMIZED WIDTH (keep button colors)
                        cat_btn = tk.Button(self.categories_frame, text=category['name'],
                                           image=photo, compound=tk.TOP,
                                           font=('Segoe UI', 10), bg='#2d2d2d', fg='white',
                                           width=162, height=90, relief='raised', bd=1,  # 10% less than 180 (180 * 0.9 = 162)
                                           command=lambda cat_id=category['id']: self.select_category(cat_id))
                        cat_btn.image = photo  # Keep a reference
                    except Exception as e:
                        print(f"Error loading category image: {e}")
                        # Fallback to text-only button - OPTIMIZED WIDTH (keep button colors)
                        cat_btn = tk.Button(self.categories_frame, text=category['name'],
                                           font=('Segoe UI', 11), bg='#2d2d2d', fg='white',
                                           width=20, height=3, relief='raised', bd=1,  # 10% less than 22 (22 * 0.9 = 19.8, rounded to 20)
                                           command=lambda cat_id=category['id']: self.select_category(cat_id))
                else:
                    # Text-only button - OPTIMIZED WIDTH (keep button colors)
                    cat_btn = tk.Button(self.categories_frame, text=category['name'],
                                       font=('Segoe UI', 11), bg='#2d2d2d', fg='white',
                                       width=20, height=3, relief='raised', bd=1,  # 10% less than 22 (22 * 0.9 = 19.8, rounded to 20)
                                       command=lambda cat_id=category['id']: self.select_category(cat_id))

                cat_btn.pack(fill=tk.X, padx=8, pady=3)
        finally:
            conn.close()

    def select_category(self, category_id):
        """Select a category and load its products"""
        self.selected_category = category_id
        self.load_products()

    def load_products(self):
        """Load products for selected category with user-configurable grid"""
        # Clear existing products
        for widget in self.products_frame.winfo_children():
            widget.destroy()

        if not self.selected_category:
            return

        # Get user display settings
        from database import get_display_settings
        display_settings = get_display_settings()

        max_columns = display_settings['max_columns']
        button_size = display_settings['button_size']

        # Calculate font size based on button size
        if button_size >= 140:
            font_size = 9
        elif button_size >= 120:
            font_size = 8
        else:
            font_size = 7

        conn = get_db_connection()
        try:
            c = conn.cursor()
            c.execute("SELECT * FROM products WHERE category_id = ? ORDER BY name",
                     (self.selected_category,))
            products = c.fetchall()

            # Display products in user-configured grid
            for i, product in enumerate(products):
                row = i // max_columns
                col = i % max_columns

                # Product button with image if available (name inside button)
                if product['image']:
                    try:
                        # Load image from database
                        from PIL import Image, ImageTk
                        import io

                        image_data = product['image']
                        image = Image.open(io.BytesIO(image_data))

                        # Resize image based on button size - BIGGER IMAGES FOR BETTER VISIBILITY
                        image_width = int(button_size * 0.85)  # 85% of button size (was 70%)
                        image_height = int(button_size * 0.65)  # 65% of button size (was 50%)
                        image = image.resize((image_width, image_height), Image.Resampling.LANCZOS)
                        photo = ImageTk.PhotoImage(image)

                        # Product button with optimized spacing - MINIMAL EMPTY SPACE (keep button colors)
                        button_text = f"{product['name']}\n{product['price']:.2f} MAD"  # Removed extra newline for tighter spacing
                        prod_btn = tk.Button(self.products_frame, image=photo, text=button_text,
                                            compound=tk.TOP, font=('Segoe UI', font_size, 'bold'),
                                            bg='#2d2d2d', fg='white', width=button_size, height=button_size,
                                            relief='raised', bd=1, pady=2,  # Reduced internal padding
                                            command=lambda p=product: self.add_to_cart(p))
                        prod_btn.image = photo  # Keep a reference
                        prod_btn.grid(row=row, column=col, padx=3, pady=3, sticky='nsew')

                    except Exception as e:
                        print(f"Error loading product image: {e}")
                        # Fallback to text-only button with proper character-based sizing - WIDER FOR TOUCH (keep button colors)
                        button_text = f"{product['name']}\n{product['price']:.2f} MAD"
                        # Convert pixel size to character units (approximate) - INCREASED MINIMUM WIDTH
                        char_width = max(12, min(24, button_size // 7))  # 12-24 characters wide (was 8-20)
                        char_height = max(3, min(7, button_size // 22))  # 3-7 characters tall (was 2-6)
                        prod_btn = tk.Button(self.products_frame, text=button_text,
                                            font=('Segoe UI', font_size, 'bold'), bg='#2d2d2d', fg='white',
                                            width=char_width, height=char_height, relief='raised', bd=1,
                                            command=lambda p=product: self.add_to_cart(p))
                        prod_btn.grid(row=row, column=col, padx=3, pady=3, sticky='nsew')
                else:
                    # Text-only button with proper character-based sizing - WIDER FOR TOUCH (keep button colors)
                    button_text = f"{product['name']}\n{product['price']:.2f} MAD"
                    # Convert pixel size to character units (approximate) - INCREASED MINIMUM WIDTH
                    char_width = max(12, min(24, button_size // 7))  # 12-24 characters wide (was 8-20)
                    char_height = max(3, min(7, button_size // 22))  # 3-7 characters tall (was 2-6)
                    prod_btn = tk.Button(self.products_frame, text=button_text,
                                        font=('Segoe UI', font_size, 'bold'), bg='#2d2d2d', fg='white',
                                        width=char_width, height=char_height, relief='raised', bd=1,
                                        command=lambda p=product: self.add_to_cart(p))
                    prod_btn.grid(row=row, column=col, padx=3, pady=3, sticky='nsew')

            # Configure grid weights for user-configured columns
            for i in range(max_columns):
                self.products_frame.grid_columnconfigure(i, weight=1)
        finally:
            conn.close()

    def add_to_cart(self, product):
        """Add product to cart"""
        # Check if product already in cart
        for item in self.app.cart_items:
            if item['id'] == product['id'] and item['type'] == 'product':
                item['quantity'] += 1
                break
        else:
            # Add new item to cart
            self.app.cart_items.append({
                'id': product['id'],
                'name': product['name'],
                'price': product['price'],
                'quantity': 1,
                'type': 'product'
            })

        self.update_cart_display()

    def update_cart_display(self):
        """Update cart display"""
        # Clear cart display
        for widget in self.cart_frame.winfo_children():
            widget.destroy()

        # Initialize selected item tracking
        if not hasattr(self, 'selected_cart_item'):
            self.selected_cart_item = None

        total = 0
        for i, item in enumerate(self.app.cart_items):
            item_total = item['price'] * item['quantity']
            total += item_total

            # Item frame - full width of cart area, clickable for selection
            item_frame = tk.Frame(self.cart_frame, bg='#404040', relief='solid', bd=1, cursor='hand2')
            item_frame.pack(fill=tk.X, pady=2, padx=2)

            # Highlight selected item
            if self.selected_cart_item == i:
                item_frame.config(bg='#ff8c00', relief='solid', bd=2)
                text_color = 'white'
                bg_color = '#ff8c00'
            else:
                text_color = 'white'
                bg_color = '#404040'

            # Item info - full width, clickable
            info_text = f"{item['name']}\n{item['quantity']} x {item['price']:.2f} = {item_total:.2f} MAD"
            item_label = tk.Label(item_frame, text=info_text,
                                 font=('Segoe UI', 8), bg=bg_color, fg=text_color,
                                 anchor='w', justify='left', cursor='hand2')
            item_label.pack(fill=tk.X, padx=5, pady=2)

            # Bind click events for selection
            item_frame.bind("<Button-1>", lambda e, idx=i: self.select_cart_item(idx))
            item_label.bind("<Button-1>", lambda e, idx=i: self.select_cart_item(idx))

        # Update total (half-size text as per user preference)
        self.total_label.config(text=f"{self.app.get_text('total')} {total:.2f} MAD")

    def select_cart_item(self, index):
        """Select a cart item for removal"""
        if self.selected_cart_item == index:
            # Deselect if clicking the same item
            self.selected_cart_item = None
        else:
            # Select the clicked item
            self.selected_cart_item = index

        # Refresh display to show selection
        self.update_cart_display()

    def add_extra_charge(self):
        """Add extra charge to cart"""
        self.show_extra_charge_dialog()

    def show_extra_charge_dialog(self):
        """Show extra charge dialog with modern orange/black design"""
        dialog = tk.Toplevel(self.root)
        dialog.title(self.app.get_text('add_extra_charge'))
        dialog.geometry("550x500")  # Much bigger to fit everything
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.configure(bg='#1a1a1a')  # Dark background

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (550 // 2)
        y = (dialog.winfo_screenheight() // 2) - (500 // 2)
        dialog.geometry(f"550x500+{x}+{y}")

        # Orange header
        header_frame = tk.Frame(dialog, bg='#ff8c00', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        header_label = tk.Label(header_frame, text=f"💰 {self.app.get_text('add_extra_charge')}",
                               font=('Segoe UI', 16, 'bold'), bg='#ff8c00', fg='white')
        header_label.pack(expand=True)

        # Variables
        amount_var = tk.StringVar()
        description_var = tk.StringVar(value="Extra")  # Default description

        # Main content frame
        main_frame = tk.Frame(dialog, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # Description section
        desc_section = tk.Frame(main_frame, bg='#2d2d2d', relief='solid', bd=1)
        desc_section.pack(fill=tk.X, pady=(0, 15))

        tk.Label(desc_section, text=f"📝 {self.app.get_text('description')} (Optional):",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', padx=15, pady=(10, 5))
        description_entry = tk.Entry(desc_section, textvariable=description_var,
                                   font=('Segoe UI', 12), bg='#404040', fg='white',
                                   insertbackground='white', relief='flat', bd=5)
        description_entry.pack(fill=tk.X, padx=15, pady=(0, 10))

        # Amount section
        amount_section = tk.Frame(main_frame, bg='#2d2d2d', relief='solid', bd=1)
        amount_section.pack(fill=tk.X, pady=(0, 15))

        tk.Label(amount_section, text=f"💰 {self.app.get_text('amount')} (MAD):",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', padx=15, pady=(10, 5))
        amount_entry = tk.Entry(amount_section, textvariable=amount_var,
                               font=('Segoe UI', 14), bg='#404040', fg='white',
                               insertbackground='white', relief='flat', bd=5)
        amount_entry.pack(fill=tk.X, padx=15, pady=(0, 10))

        # Keyboard button
        keyboard_btn = tk.Button(main_frame, text=f"📱 {self.app.get_text('show_keyboard')}",
                               font=('Segoe UI', 12, 'bold'), bg='#ff8c00', fg='white',
                               relief='flat', bd=0, padx=20, pady=10,
                               activebackground='#e67e00', activeforeground='white',
                               command=lambda: self.show_extra_charge_keyboard(amount_entry, dialog))
        keyboard_btn.pack(pady=(0, 20))

        # Buttons frame
        buttons_frame = tk.Frame(main_frame, bg='#1a1a1a')
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        # Add button
        add_btn = tk.Button(buttons_frame, text=f"✅ {self.app.get_text('add')}",
                           font=('Segoe UI', 12, 'bold'), bg='#28a745', fg='white',
                           relief='flat', bd=0, padx=25, pady=10,
                           activebackground='#218838', activeforeground='white',
                           command=lambda: self.process_extra_charge(dialog, amount_var, description_var))
        add_btn.pack(side=tk.LEFT, padx=(0, 15))

        # Cancel button
        cancel_btn = tk.Button(buttons_frame, text=f"❌ {self.app.get_text('cancel')}",
                              font=('Segoe UI', 12, 'bold'), bg='#6c757d', fg='white',
                              relief='flat', bd=0, padx=25, pady=10,
                              activebackground='#5a6268', activeforeground='white',
                              command=dialog.destroy)
        cancel_btn.pack(side=tk.LEFT)

        # Focus on amount entry
        amount_entry.focus()

    def show_extra_charge_keyboard(self, target_entry, parent_dialog):
        """Show number keyboard for extra charge amount"""
        from number_keyboard import NumberKeyboard

        keyboard = NumberKeyboard(parent_dialog, target_entry,
                                 self.app.get_text('enter_amount'),
                                 pos_system=self.app, numbers_only=False)  # Allow decimal point
        keyboard.show()

    def process_extra_charge(self, dialog, amount_var, description_var):
        """Process the extra charge and add to cart"""
        amount_str = amount_var.get().strip()
        description = description_var.get().strip()

        # Validation
        if not amount_str:
            messagebox.showerror(self.app.get_text('error'),
                               "Please enter an amount")
            return

        try:
            amount = float(amount_str)
            if amount <= 0:
                messagebox.showerror(self.app.get_text('error'),
                                   "Amount must be greater than 0")
                return
        except ValueError:
            messagebox.showerror(self.app.get_text('error'),
                               "Please enter a valid amount")
            return

        # Use default description if none provided
        if not description:
            description = "Extra"

        # Add extra charge to cart
        self.app.cart_items.append({
            'id': f'extra_{len(self.app.cart_items)}',  # Unique ID for extra charges
            'name': description,
            'price': amount,
            'quantity': 1,
            'type': 'extra'
        })

        # Close dialog and update cart
        dialog.destroy()
        self.update_cart_display()

        messagebox.showinfo(self.app.get_text('success'),
                          f"Added extra charge: {description} - {amount:.2f} MAD")

    def remove_selected(self):
        """Remove selected item from cart"""
        if not self.app.cart_items:
            messagebox.showwarning(self.app.get_text('warning'),
                                 "Cart is empty")
            return

        if not hasattr(self, 'selected_cart_item') or self.selected_cart_item is None:
            messagebox.showwarning(self.app.get_text('warning'),
                                 "Please select an item to remove")
            return

        if 0 <= self.selected_cart_item < len(self.app.cart_items):
            # Get item name for confirmation
            item_name = self.app.cart_items[self.selected_cart_item]['name']

            if messagebox.askyesno(self.app.get_text('confirm'),
                                 f"Remove '{item_name}' from cart?"):
                # Remove the selected item
                self.app.cart_items.pop(self.selected_cart_item)
                self.selected_cart_item = None  # Clear selection
                self.update_cart_display()

    def clear_cart(self):
        """Clear all items from cart"""
        if self.app.cart_items:
            if messagebox.askyesno(self.app.get_text('confirm'),
                                 "Are you sure you want to clear the cart?"):
                self.app.cart_items.clear()
                self.update_cart_display()

    def checkout(self):
        """Process checkout with professional receipt printing"""
        if not self.app.cart_items:
            messagebox.showwarning(self.app.get_text('warning'),
                                 self.app.get_text('cart_empty'))
            return

        total = sum(item['price'] * item['quantity'] for item in self.app.cart_items)
        user_name = self.app.current_user['username']

        # Save sale to database
        conn = get_db_connection()
        try:
            c = conn.cursor()

            # Create items string for the sales table
            items_list = []
            for item in self.app.cart_items:
                items_list.append(f"{item['name']} ({item['quantity']})")
            items_str = ", ".join(items_list)

            c.execute("""
                INSERT INTO sales (user_id, total, date, items)
                VALUES (?, ?, ?, ?)
            """, (self.app.current_user['id'], total, datetime.now().isoformat(), items_str))

            sale_id = c.lastrowid

            # Save sale items
            for item in self.app.cart_items:
                c.execute("""
                    INSERT INTO sale_items (sale_id, product_name, quantity, price, is_extra)
                    VALUES (?, ?, ?, ?, ?)
                """, (sale_id, item['name'], item['quantity'], item['price'],
                     1 if item['type'] == 'extra' else 0))

            conn.commit()

            # Reduce stock for sold items
            self.reduce_stock_for_sale()

            # Generate and print receipts
            try:
                from receipt_generator import ReceiptGenerator
                receipt_gen = ReceiptGenerator(self.app)

                # Generate receipt number
                receipt_number = f"R{sale_id:06d}"

                # 1. Customer Receipt (Full details)
                customer_receipt = receipt_gen.generate_customer_receipt(
                    self.app.cart_items, [], total, user_name, receipt_number
                )

                # 2. Summary Receipt (Brief internal copy)
                summary_receipt = receipt_gen.generate_summary_receipt(
                    user_name, total, f"S{sale_id:06d}"
                )

                # Print both receipts
                print_success = True

                try:
                    # Print customer receipt
                    if not receipt_gen.print_receipt(customer_receipt, "Customer Receipt"):
                        print_success = False

                    # Print summary receipt
                    if not receipt_gen.print_receipt(summary_receipt, "Summary Receipt"):
                        print_success = False

                except Exception as e:
                    print_success = False
                    print(f"Printing error: {e}")

                # Show success message
                success_msg = f"{self.app.get_text('sale_completed')} {total:.2f} MAD\n"
                success_msg += f"Receipt #: {receipt_number}\n"

                if print_success:
                    success_msg += "✓ Receipts printed successfully"
                else:
                    success_msg += "⚠ Printing failed - check printer connection"

                messagebox.showinfo(self.app.get_text('success'), success_msg)

            except ImportError:
                # Fallback if receipt generator not available
                messagebox.showinfo(self.app.get_text('success'),
                                  f"{self.app.get_text('sale_completed')} {total:.2f} MAD")

            # Clear cart
            self.app.cart_items.clear()
            self.update_cart_display()

        except Exception as e:
            messagebox.showerror(self.app.get_text('error'),
                               f"{self.app.get_text('failed_complete_sale')}: {e}")
        finally:
            conn.close()

    def reduce_stock_for_sale(self):
        """Reduce stock quantities for sold items"""
        try:
            # Import storage management for stock reduction
            if hasattr(self.app, 'storage_management') and self.app.storage_management:
                storage_mgr = self.app.storage_management
            else:
                from storage_management import StorageManagement
                storage_mgr = StorageManagement(self.app)

            # Reduce stock for each product sold
            for item in self.app.cart_items:
                if item['type'] == 'product':  # Only reduce stock for actual products, not extras
                    # Get product ID from database
                    conn = get_db_connection()
                    try:
                        c = conn.cursor()
                        c.execute("SELECT id FROM products WHERE name = ?", (item['name'],))
                        result = c.fetchone()
                        if result:
                            product_id = result['id']
                            # Reduce stock
                            storage_mgr.reduce_stock_on_sale(product_id, item['quantity'])
                    finally:
                        conn.close()

        except Exception as e:
            print(f"Error reducing stock: {e}")
            # Don't fail the sale if stock reduction fails

    def update_clock(self):
        """Update the clock display"""
        if self.clock_label:
            now = datetime.now()
            # Format: hh:mm --- dd/mm/yyyy (removed seconds for performance)
            time_str = now.strftime("%H:%M --- %d/%m/%Y")
            self.clock_label.config(text=time_str)

            # Schedule next update every 30 seconds for better performance
            self.root.after(30000, self.update_clock)

    def show_display_settings(self):
        """Show display settings dialog with modern orange/black design"""
        from database import get_display_settings, save_display_settings
        import tkinter.messagebox as messagebox

        # Get current settings
        current_settings = get_display_settings()

        # Create dialog
        dialog = tk.Toplevel(self.root)
        dialog.title("Display Settings")
        dialog.geometry("500x450")  # Bigger size
        dialog.configure(bg='#1a1a1a')  # Dark background
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (dialog.winfo_screenheight() // 2) - (450 // 2)
        dialog.geometry(f"500x450+{x}+{y}")

        # Orange header
        header_frame = tk.Frame(dialog, bg='#ff8c00', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        header_label = tk.Label(header_frame, text="🖥️ Display Settings",
                               font=('Segoe UI', 16, 'bold'), bg='#ff8c00', fg='white')
        header_label.pack(expand=True)

        # Main content frame
        main_frame = tk.Frame(dialog, bg='#1a1a1a')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # Max columns section
        columns_section = tk.Frame(main_frame, bg='#2d2d2d', relief='solid', bd=1)
        columns_section.pack(fill=tk.X, pady=(0, 15))

        tk.Label(columns_section, text="📊 Maximum Columns:",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', padx=15, pady=(10, 5))

        columns_frame = tk.Frame(columns_section, bg='#2d2d2d')
        columns_frame.pack(fill=tk.X, padx=15, pady=(0, 10))

        tk.Label(columns_frame, text="Number of product columns:",
                font=('Segoe UI', 10), bg='#2d2d2d', fg='white').pack(side=tk.LEFT)

        columns_var = tk.IntVar(value=current_settings['max_columns'])
        columns_spinbox = tk.Spinbox(columns_frame, from_=2, to=8, width=8,
                                    textvariable=columns_var, font=('Segoe UI', 11),
                                    bg='#404040', fg='white', insertbackground='white')
        columns_spinbox.pack(side=tk.RIGHT)

        # Button size section
        size_section = tk.Frame(main_frame, bg='#2d2d2d', relief='solid', bd=1)
        size_section.pack(fill=tk.X, pady=(0, 15))

        tk.Label(size_section, text="📏 Button Size:",
                font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='#ff8c00').pack(anchor='w', padx=15, pady=(10, 5))

        size_frame = tk.Frame(size_section, bg='#2d2d2d')
        size_frame.pack(fill=tk.X, padx=15, pady=(0, 10))

        tk.Label(size_frame, text="Product button size (pixels):",
                font=('Segoe UI', 10), bg='#2d2d2d', fg='white').pack(side=tk.LEFT)

        size_var = tk.IntVar(value=current_settings['button_size'])
        size_spinbox = tk.Spinbox(size_frame, from_=80, to=200, width=8,
                                 textvariable=size_var, font=('Segoe UI', 11),
                                 bg='#404040', fg='white', insertbackground='white')
        size_spinbox.pack(side=tk.RIGHT)

        # Preview info
        info_frame = tk.Frame(main_frame, bg='#404040', relief='solid', bd=1)
        info_frame.pack(fill=tk.X, pady=(15, 0))

        info_label = tk.Label(info_frame, text="💡 Tip: Smaller screens work better with fewer columns and smaller buttons",
                             font=('Segoe UI', 10), bg='#404040', fg='white', wraplength=400)
        info_label.pack(padx=15, pady=15)

        # Buttons frame
        buttons_frame = tk.Frame(main_frame, bg='#1a1a1a')
        buttons_frame.pack(fill=tk.X, pady=(25, 0))

        # Save button
        def save_settings():
            max_cols = columns_var.get()
            btn_size = size_var.get()

            if save_display_settings(max_cols, btn_size):
                messagebox.showinfo("Success", "Display settings saved!\nReload products to see changes.")
                dialog.destroy()
                # Refresh products display if a category is selected
                if self.selected_category:
                    self.load_products()
            else:
                messagebox.showerror("Error", "Failed to save display settings.")

        save_btn = tk.Button(buttons_frame, text="💾 Save Settings",
                            font=('Segoe UI', 12, 'bold'), bg='#28a745', fg='white',
                            relief='flat', bd=0, padx=25, pady=10,
                            activebackground='#218838', activeforeground='white',
                            command=save_settings)
        save_btn.pack(side=tk.RIGHT, padx=(15, 0))

        # Cancel button
        cancel_btn = tk.Button(buttons_frame, text="❌ Cancel",
                              font=('Segoe UI', 12, 'bold'), bg='#6c757d', fg='white',
                              relief='flat', bd=0, padx=25, pady=10,
                              activebackground='#5a6268', activeforeground='white',
                              command=dialog.destroy)
        cancel_btn.pack(side=tk.RIGHT)




