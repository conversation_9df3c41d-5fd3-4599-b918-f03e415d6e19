"""
Sales History Screen
Provides sales reporting and history management
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta

from database import get_db_connection

class SalesHistory:
    """Sales history and reporting interface"""

    def __init__(self, app):
        self.app = app
        self.root = app.root
        self.frame = None
        self.sales_tree = None

        # Filter state tracking
        self.today_filter_active = False
        self.date_range_filter_active = False

    def show(self):
        """Display the sales history screen"""
        self.root.configure(bg='#1a1a1a')

        # Create main frame
        self.frame = tk.Frame(self.root, bg='#1a1a1a')
        self.frame.pack(fill=tk.BOTH, expand=True)

        # Create interface
        self.create_interface()
        self.load_sales()

    def hide(self):
        """Hide the sales history screen"""
        if self.frame:
            self.frame.destroy()
            self.frame = None

    def refresh_language(self):
        """Refresh the screen with new language"""
        if self.frame:
            self.hide()
            self.show()

    def create_interface(self):
        """Create the sales history interface"""
        # Header
        header_frame = tk.Frame(self.frame, bg='#2d2d2d', height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # Title
        title_label = tk.Label(header_frame, text=self.app.get_text('sales_history'),
                              font=('Segoe UI', 16, 'bold'), bg='#2d2d2d', fg='white')
        title_label.pack(side=tk.LEFT, padx=20, pady=15)

        # Back button
        back_btn = tk.Button(header_frame, text=self.app.get_text('back'),
                            font=('Segoe UI', 10, 'bold'), bg='#6c757d', fg='white',
                            padx=15, pady=5, command=self.app.show_pos_screen)
        back_btn.pack(side=tk.RIGHT, padx=20, pady=10)

        # Main content
        content_frame = tk.Frame(self.frame, bg='#1a1a1a')
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Filter controls
        self.create_filter_controls(content_frame)

        # Sales list
        self.create_sales_list(content_frame)

        # Total display
        self.create_total_display(content_frame)

        # Action buttons
        self.create_action_buttons(content_frame)

    def create_filter_controls(self, parent):
        """Create filter controls"""
        filter_frame = tk.LabelFrame(parent, text="Filters", font=('Segoe UI', 10, 'bold'), bg='#2d2d2d', fg='white')
        filter_frame.pack(fill=tk.X, pady=(0, 20))

        # Main filter container
        main_filter_frame = tk.Frame(filter_frame, bg='#2d2d2d')
        main_filter_frame.pack(padx=10, pady=10, fill=tk.X)

        # Row 1: Quick filters and Today settings
        row1_frame = tk.Frame(main_filter_frame, bg='#2d2d2d')
        row1_frame.pack(fill=tk.X, pady=(0, 10))

        # Today filter button only
        today_button_frame = tk.Frame(row1_frame, bg='#2d2d2d')
        today_button_frame.pack(side=tk.LEFT)

        filter_today_btn = tk.Button(today_button_frame, text=self.app.get_text('filter_today'),
                                    font=('Segoe UI', 10, 'bold'), bg='#28a745', fg='white',
                                    padx=20, pady=8, command=self.filter_today)
        filter_today_btn.pack()

        # Today settings
        today_settings_frame = tk.Frame(row1_frame, bg='#2d2d2d')
        today_settings_frame.pack(side=tk.RIGHT)

        tk.Label(today_settings_frame, text="Today starts at:",
                font=('Segoe UI', 9), bg='#2d2d2d', fg='white').pack(side=tk.LEFT, padx=(0, 5))

        # Load saved history reset time
        from database import load_history_reset_time
        saved_hour, saved_minute = load_history_reset_time()

        self.today_start_hour = tk.StringVar(value=f"{saved_hour:02d}")
        hour_spinbox = tk.Spinbox(today_settings_frame, from_=0, to=23, width=3,
                                 textvariable=self.today_start_hour, format="%02.0f",
                                 command=self.save_reset_time)
        hour_spinbox.pack(side=tk.LEFT, padx=(0, 2))

        tk.Label(today_settings_frame, text=":", font=('Segoe UI', 9), bg='#2d2d2d', fg='white').pack(side=tk.LEFT)

        self.today_start_minute = tk.StringVar(value=f"{saved_minute:02d}")
        minute_spinbox = tk.Spinbox(today_settings_frame, from_=0, to=59, width=3,
                                   textvariable=self.today_start_minute, format="%02.0f",
                                   command=self.save_reset_time)
        minute_spinbox.pack(side=tk.LEFT, padx=(2, 0))

        # Row 2: Date range and user filters
        row2_frame = tk.Frame(main_filter_frame, bg='#2d2d2d')
        row2_frame.pack(fill=tk.X, pady=(0, 10))

        # Date range filter
        date_range_frame = tk.Frame(row2_frame, bg='#2d2d2d')
        date_range_frame.pack(side=tk.LEFT)

        tk.Label(date_range_frame, text="Date Range:",
                font=('Segoe UI', 9, 'bold'), bg='#2d2d2d', fg='white').pack(side=tk.LEFT, padx=(0, 10))

        tk.Label(date_range_frame, text="From:",
                font=('Segoe UI', 9), bg='#2d2d2d', fg='white').pack(side=tk.LEFT, padx=(0, 5))

        self.from_date = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        from_date_btn = tk.Button(date_range_frame, textvariable=self.from_date,
                                 font=('Segoe UI', 9), bg='#404040', fg='white',
                                 width=12, relief='solid', bd=1,
                                 command=lambda: self.show_date_picker('from'))
        from_date_btn.pack(side=tk.LEFT, padx=(0, 10))

        tk.Label(date_range_frame, text="To:",
                font=('Segoe UI', 9), bg='#2d2d2d', fg='white').pack(side=tk.LEFT, padx=(0, 5))

        self.to_date = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        to_date_btn = tk.Button(date_range_frame, textvariable=self.to_date,
                               font=('Segoe UI', 9), bg='#404040', fg='white',
                               width=12, relief='solid', bd=1,
                               command=lambda: self.show_date_picker('to'))
        to_date_btn.pack(side=tk.LEFT, padx=(0, 10))

        date_filter_btn = tk.Button(date_range_frame, text="Apply Range",
                                   font=('Segoe UI', 9, 'bold'), bg='#ffc107', fg='white',
                                   padx=10, pady=3, command=self.filter_date_range)
        date_filter_btn.pack(side=tk.LEFT)

        # User filter
        user_filter_frame = tk.Frame(row2_frame, bg='#2d2d2d')
        user_filter_frame.pack(side=tk.RIGHT)

        tk.Label(user_filter_frame, text="User:",
                font=('Segoe UI', 9, 'bold'), bg='#2d2d2d', fg='white').pack(side=tk.LEFT, padx=(0, 5))

        self.selected_user = tk.StringVar(value="All Users")
        self.user_combobox = ttk.Combobox(user_filter_frame, textvariable=self.selected_user,
                                         width=15, state="readonly")
        self.user_combobox.pack(side=tk.LEFT, padx=(0, 10))
        self.user_combobox.bind('<<ComboboxSelected>>', self.filter_by_user)

        # Load users for filter
        self.load_users_for_filter()

    def create_sales_list(self, parent):
        """Create sales list display"""
        # Sales header with title and instruction
        sales_header_frame = tk.Frame(parent, bg='#1a1a1a')
        sales_header_frame.pack(fill=tk.X, pady=(0, 5))

        # Title on the left
        title_label = tk.Label(sales_header_frame, text=self.app.get_text('sales_history'),
                              font=('Segoe UI', 12, 'bold'), bg='#1a1a1a', fg='white')
        title_label.pack(side=tk.LEFT)

        # Instruction on the right
        instruction_label = tk.Label(sales_header_frame, text=f"💡 {self.app.get_text('double_click_instruction')}",
                                   font=('Segoe UI', 9, 'italic'), bg='#1a1a1a', fg='#6c757d')
        instruction_label.pack(side=tk.RIGHT, padx=(20, 0))

        # Sales frame without title (since we have it above)
        sales_frame = tk.Frame(parent, bg='#2d2d2d', relief=tk.RAISED, bd=1)
        sales_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Treeview for sales
        columns = ('ID', 'Date', 'Time', 'User', 'Total', 'Items')
        self.sales_tree = ttk.Treeview(sales_frame, columns=columns, show='headings', height=15)

        # Configure columns
        self.sales_tree.heading('ID', text='ID')
        self.sales_tree.heading('Date', text=self.app.get_text('date'))
        self.sales_tree.heading('Time', text=self.app.get_text('time'))
        self.sales_tree.heading('User', text=self.app.get_text('user_column'))
        self.sales_tree.heading('Total', text=self.app.get_text('total_column'))
        self.sales_tree.heading('Items', text=self.app.get_text('items'))

        self.sales_tree.column('ID', width=50)
        self.sales_tree.column('Date', width=100)
        self.sales_tree.column('Time', width=80)
        self.sales_tree.column('User', width=100)
        self.sales_tree.column('Total', width=100)
        self.sales_tree.column('Items', width=200)

        # Configure alternating row colors for better readability
        self.sales_tree.tag_configure('oddrow', background='#f8f9fa')
        self.sales_tree.tag_configure('evenrow', background='white')

        # Bind double-click event for transaction inspection
        self.sales_tree.bind('<Double-1>', self.inspect_transaction)

        # Scrollbar
        scrollbar = ttk.Scrollbar(sales_frame, orient=tk.VERTICAL, command=self.sales_tree.yview)
        self.sales_tree.configure(yscrollcommand=scrollbar.set)

        # Pack
        self.sales_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def create_total_display(self, parent):
        """Create total display for current sales view"""
        # Total display frame
        total_frame = tk.Frame(parent, bg='#1a1a1a')
        total_frame.pack(fill=tk.X, pady=(10, 10))

        # Center the total display
        center_frame = tk.Frame(total_frame, bg='#2d2d2d', relief=tk.RAISED, bd=2)
        center_frame.pack(anchor=tk.CENTER)

        # Total label
        self.total_label = tk.Label(center_frame, text="Total: 0.00 MAD",
                                   font=('Segoe UI', 14, 'bold'),
                                   bg='#2d2d2d', fg='white',
                                   padx=30, pady=10)
        self.total_label.pack()

    def create_action_buttons(self, parent):
        """Create action buttons"""
        buttons_frame = tk.Frame(parent, bg='#1a1a1a')
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        # Left side - Print and Email buttons
        left_frame = tk.Frame(buttons_frame, bg='#1a1a1a')
        left_frame.pack(side=tk.LEFT)

        # Print button
        if self.app.icons.get('checkout'):
            print_btn = tk.Button(left_frame, image=self.app.icons['checkout'],
                                 text=self.app.get_text('print_history'),
                                 compound=tk.LEFT, font=('Segoe UI', 10, 'bold'),
                                 bg='#17a2b8', fg='white', padx=20, pady=8,
                                 command=self.show_print_options)
        else:
            print_btn = tk.Button(left_frame, text=self.app.get_text('print_history'),
                                 font=('Segoe UI', 10, 'bold'), bg='#17a2b8', fg='white',
                                 padx=20, pady=8, command=self.show_print_options)
        print_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Email button (icon only)
        if self.app.icons.get('email'):
            email_btn = tk.Button(left_frame, image=self.app.icons['email'],
                                 font=('Segoe UI', 10, 'bold'),
                                 bg='#28a745', fg='white', padx=15, pady=8,
                                 command=self.show_email_settings)
        else:
            email_btn = tk.Button(left_frame, text="📧",
                                 font=('Segoe UI', 12, 'bold'), bg='#28a745', fg='white',
                                 padx=15, pady=8, command=self.show_email_settings)
        email_btn.pack(side=tk.LEFT)

        # Right side - Delete all history button (admin only, far from print)
        if self.app.current_user.get('is_admin'):
            right_frame = tk.Frame(buttons_frame, bg='#1a1a1a')
            right_frame.pack(side=tk.RIGHT)

            if self.app.icons.get('clear_cart'):
                delete_all_btn = tk.Button(right_frame, image=self.app.icons['clear_cart'],
                                          text=self.app.get_text('delete_all_history'),
                                          compound=tk.LEFT, font=('Segoe UI', 10, 'bold'),
                                          bg='#dc3545', fg='white', padx=20, pady=8,
                                          command=self.delete_all_history)
            else:
                delete_all_btn = tk.Button(right_frame, text=self.app.get_text('delete_all_history'),
                                          font=('Segoe UI', 10, 'bold'), bg='#dc3545', fg='white',
                                          padx=20, pady=8, command=self.delete_all_history)
            delete_all_btn.pack()

    def load_sales(self, filter_condition=None):
        """Load sales from database with safe connection management"""
        # Clear existing items
        for item in self.sales_tree.get_children():
            self.sales_tree.delete(item)

        from database import safe_db_connection

        try:
            with safe_db_connection() as conn:
                c = conn.cursor()

                # Base query
                query = """
                    SELECT s.id, s.date, s.total, u.username,
                           GROUP_CONCAT(si.product_name || ' (' || si.quantity || ')') as items
                    FROM sales s
                    LEFT JOIN users u ON s.user_id = u.id
                    LEFT JOIN sale_items si ON s.id = si.sale_id
                """

                params = []
                if filter_condition:
                    query += " WHERE " + filter_condition[0]
                    params = filter_condition[1]

                query += " GROUP BY s.id ORDER BY s.date DESC"

                c.execute(query, params)
                sales = c.fetchall()

                # Calculate total for display
                total_amount = 0.0

                for index, sale in enumerate(sales):
                    # Add to total
                    total_amount += sale['total']

                    # Parse datetime
                    try:
                        sale_datetime = datetime.fromisoformat(sale['date'])
                        date_str = sale_datetime.strftime('%d/%m/%Y')
                        time_str = sale_datetime.strftime('%H:%M:%S')
                    except:
                        date_str = sale['date'][:10] if sale['date'] else 'N/A'
                        time_str = sale['date'][11:19] if len(sale['date']) > 10 else 'N/A'

                    items_text = sale['items'] or 'No items'
                    if len(items_text) > 50:
                        items_text = items_text[:47] + '...'

                    # Determine row color tag for alternating colors
                    row_tag = 'evenrow' if index % 2 == 0 else 'oddrow'

                    self.sales_tree.insert('', tk.END, values=(
                        sale['id'], date_str, time_str,
                        sale['username'] or 'Unknown',
                        f"{sale['total']:.2f} MAD",
                        items_text
                    ), tags=(row_tag,))

                # Update total display
                self.update_total_display(total_amount, len(sales))

        except Exception as e:
            print(f"Error loading sales: {e}")
            # Show error to user but don't crash
            self.update_total_display(0.0, 0)

    def update_total_display(self, total_amount, transaction_count):
        """Update the total display with current sales total"""
        if hasattr(self, 'total_label'):
            if transaction_count == 0:
                self.total_label.config(text="No sales found")
            elif transaction_count == 1:
                self.total_label.config(text=f"Total: {total_amount:.2f} MAD (1 transaction)")
            else:
                self.total_label.config(text=f"Total: {total_amount:.2f} MAD ({transaction_count} transactions)")

    def inspect_transaction(self, event):
        """Inspect selected transaction in detail"""
        selection = self.sales_tree.selection()
        if not selection:
            return

        # Get selected transaction ID
        item = self.sales_tree.item(selection[0])
        transaction_id = item['values'][0]

        # Get detailed transaction data
        transaction_data = self.get_transaction_details(transaction_id)
        if not transaction_data:
            messagebox.showerror(self.app.get_text('error'), self.app.get_text('could_not_load_details'))
            return

        # Show transaction inspection dialog
        self.show_transaction_inspection_dialog(transaction_data)

    def get_transaction_details(self, transaction_id):
        """Get detailed transaction information with safe connection management"""
        from database import safe_db_connection

        try:
            with safe_db_connection() as conn:
                c = conn.cursor()

                # Get sale details including items text
                c.execute("""
                    SELECT s.id, s.date, s.total, u.username, s.user_id, s.items
                    FROM sales s
                    LEFT JOIN users u ON s.user_id = u.id
                    WHERE s.id = ?
                """, (transaction_id,))

                sale = c.fetchone()
                if not sale:
                    print(f"No sale found for ID: {transaction_id}")
                    return None

                # Try to get detailed items from sale_items table
                c.execute("""
                    SELECT si.product_name, si.quantity, si.price
                    FROM sale_items si
                    WHERE si.sale_id = ?
                    ORDER BY si.id
                """, (transaction_id,))

                detailed_items = c.fetchall()

                # If no detailed items, parse from items text field
                items = []
                if detailed_items:
                    # Use detailed items from sale_items table
                    for item in detailed_items:
                        items.append({
                            'product_name': item['product_name'],
                            'quantity': item['quantity'],
                            'price': item['price'],
                            'line_total': item['quantity'] * item['price']
                        })
                else:
                    # Parse from items text field (fallback)
                    items_text = sale['items'] or ""
                    if items_text:
                        # Parse "Product1 (2), Product2 (1)" format
                        item_parts = items_text.split(", ")
                        for item_part in item_parts:
                            if "(" in item_part and ")" in item_part:
                                name = item_part.split(" (")[0].strip()
                                qty_str = item_part.split("(")[1].split(")")[0].strip()
                                try:
                                    quantity = int(qty_str)
                                    items.append({
                                        'product_name': name,
                                        'quantity': quantity,
                                        'price': 0.0,  # Price not available in text format
                                        'line_total': 0.0  # Total not available in text format
                                    })
                                except ValueError:
                                    continue

                return {
                    'sale': sale,
                    'items': items,
                    'extra_charges': []  # Simplified - no extra charges for now
                }

        except Exception as e:
            print(f"Error getting transaction details: {e}")
            return None

    def show_transaction_inspection_dialog(self, transaction_data):
        """Show detailed transaction inspection dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title(self.app.get_text('transaction_details_title'))
        dialog.geometry("700x600")
        dialog.resizable(True, True)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (350)
        y = (dialog.winfo_screenheight() // 2) - (300)
        dialog.geometry(f"700x600+{x}+{y}")

        # Main frame with scrollbar
        main_frame = tk.Frame(dialog, bg='#2d2d2d')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Transaction header
        sale = transaction_data['sale']
        items = transaction_data['items']
        extra_charges = transaction_data['extra_charges']

        # Parse datetime
        try:
            sale_datetime = datetime.fromisoformat(sale['date'])
            date_str = sale_datetime.strftime('%d/%m/%Y')
            time_str = sale_datetime.strftime('%H:%M:%S')
        except:
            date_str = sale['date'][:10] if sale['date'] else 'N/A'
            time_str = sale['date'][11:19] if len(sale['date']) > 10 else 'N/A'

        # Header section
        header_frame = tk.Frame(main_frame, bg='#2d2d2d')
        header_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = tk.Label(header_frame, text=f"{self.app.get_text('transaction_number')}{sale['id']}",
                              font=('Segoe UI', 16, 'bold'), bg='#2d2d2d', fg='white')
        title_label.pack(pady=15)

        # Transaction info
        info_frame = tk.Frame(main_frame, bg='#404040', relief=tk.RAISED, bd=1)
        info_frame.pack(fill=tk.X, pady=(0, 20))

        info_grid = tk.Frame(info_frame, bg='#404040')
        info_grid.pack(padx=15, pady=15)

        # Info labels
        tk.Label(info_grid, text=self.app.get_text('date_colon'), font=('Segoe UI', 10, 'bold'),
                bg='#404040', fg='white').grid(row=0, column=0, sticky='w', padx=(0, 10))
        tk.Label(info_grid, text=date_str, font=('Segoe UI', 10),
                bg='#404040', fg='white').grid(row=0, column=1, sticky='w', padx=(0, 30))

        tk.Label(info_grid, text=self.app.get_text('time_colon'), font=('Segoe UI', 10, 'bold'),
                bg='#404040', fg='white').grid(row=0, column=2, sticky='w', padx=(0, 10))
        tk.Label(info_grid, text=time_str, font=('Segoe UI', 10),
                bg='#404040', fg='white').grid(row=0, column=3, sticky='w')

        tk.Label(info_grid, text=self.app.get_text('cashier_colon'), font=('Segoe UI', 10, 'bold'),
                bg='#404040', fg='white').grid(row=1, column=0, sticky='w', padx=(0, 10), pady=(5, 0))
        tk.Label(info_grid, text=sale['username'] or 'Unknown', font=('Segoe UI', 10),
                bg='#404040', fg='white').grid(row=1, column=1, sticky='w', padx=(0, 30), pady=(5, 0))

        tk.Label(info_grid, text=self.app.get_text('total_colon'), font=('Segoe UI', 10, 'bold'),
                bg='#404040', fg='white').grid(row=1, column=2, sticky='w', padx=(0, 10), pady=(5, 0))
        tk.Label(info_grid, text=f"{sale['total']:.2f} MAD", font=('Segoe UI', 10, 'bold'),
                bg='#404040', fg='#28a745').grid(row=1, column=3, sticky='w', pady=(5, 0))

        # Items section - simplified to show just names and quantities with scrolling
        items_frame = tk.LabelFrame(main_frame, text=self.app.get_text('items_sold'),
                                   font=('Segoe UI', 12, 'bold'), bg='#2d2d2d', fg='white')
        items_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Create scrollable frame for items
        items_canvas = tk.Canvas(items_frame, bg='#2d2d2d', highlightthickness=0)
        items_scrollbar = ttk.Scrollbar(items_frame, orient="vertical", command=items_canvas.yview)
        items_scrollable_frame = tk.Frame(items_canvas, bg='#2d2d2d')

        items_scrollable_frame.bind(
            "<Configure>",
            lambda e: items_canvas.configure(scrollregion=items_canvas.bbox("all"))
        )

        items_canvas.create_window((0, 0), window=items_scrollable_frame, anchor="nw")
        items_canvas.configure(yscrollcommand=items_scrollbar.set)

        # Pack canvas and scrollbar
        items_canvas.pack(side="left", fill="both", expand=True, padx=15, pady=15)
        items_scrollbar.pack(side="right", fill="y")

        # Add items to scrollable frame
        if items:
            for index, item in enumerate(items):
                item_row = tk.Frame(items_scrollable_frame, bg='#2d2d2d')
                item_row.pack(fill=tk.X, pady=2)

                # Product name and quantity
                product_text = f"• {item['product_name']} × {item['quantity']}"

                # Show price if available
                if item['price'] > 0:
                    product_text += f" @ {item['price']:.2f} MAD {self.app.get_text('each')}"

                item_label = tk.Label(item_row, text=product_text,
                                    font=('Segoe UI', 11), bg='#2d2d2d', fg='white', anchor='w')
                item_label.pack(side=tk.LEFT, fill=tk.X, expand=True)
        else:
            no_items_label = tk.Label(items_scrollable_frame, text=self.app.get_text('no_items_found'),
                                    font=('Segoe UI', 11, 'italic'), bg='#2d2d2d', fg='#6c757d')
            no_items_label.pack(pady=20)

        # Enable mouse wheel scrolling
        def _on_mousewheel(event):
            items_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        items_canvas.bind("<MouseWheel>", _on_mousewheel)

        # Close button
        close_btn = tk.Button(main_frame, text=self.app.get_text('close'),
                             font=('Segoe UI', 12, 'bold'), bg='#6c757d', fg='white',
                             padx=30, pady=10, command=dialog.destroy)
        close_btn.pack(pady=(20, 0))

    def save_reset_time(self):
        """Save the history reset time when changed"""
        try:
            hour = int(self.today_start_hour.get())
            minute = int(self.today_start_minute.get())

            from database import save_history_reset_time
            save_history_reset_time(hour, minute)
        except Exception as e:
            print(f"Error saving reset time: {e}")

    def filter_today(self):
        """Filter sales for today (customizable start time, always 24 hours)"""
        # Set flag to indicate today filter is active
        self.today_filter_active = True
        self.date_range_filter_active = False

        # Apply combined filters (today + any active user filter)
        self.apply_combined_filters()



    def filter_date_range(self):
        """Filter sales by date range"""
        try:
            from_date_str = self.from_date.get()
            to_date_str = self.to_date.get()

            # Parse dates
            from_date = datetime.strptime(from_date_str, '%Y-%m-%d')
            to_date = datetime.strptime(to_date_str, '%Y-%m-%d') + timedelta(days=1)  # Include full day

            if from_date > to_date:
                messagebox.showerror("Error", "From date must be before To date")
                return

            # Set flag to indicate date range filter is active
            self.today_filter_active = False
            self.date_range_filter_active = True

            # Apply combined filters (date range + any active user filter)
            self.apply_combined_filters()

        except ValueError:
            messagebox.showerror("Error", "Invalid date format. Use YYYY-MM-DD")

    def show_date_picker(self, date_type):
        """Show visual date picker dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"Select {date_type.title()} Date")
        dialog.geometry("400x500")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (500 // 2)
        dialog.geometry(f"400x500+{x}+{y}")

        # Get current date
        current_date = datetime.now()
        if date_type == 'from':
            try:
                current_date = datetime.strptime(self.from_date.get(), '%Y-%m-%d')
            except:
                pass
        else:
            try:
                current_date = datetime.strptime(self.to_date.get(), '%Y-%m-%d')
            except:
                pass

        # Variables for date selection
        selected_year = tk.IntVar(value=current_date.year)
        selected_month = tk.IntVar(value=current_date.month)
        selected_day = tk.IntVar(value=current_date.day)

        # Main frame
        main_frame = tk.Frame(dialog, bg='#2d2d2d')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = tk.Label(main_frame, text=f"Select {date_type.title()} Date",
                              font=('Segoe UI', 14, 'bold'), bg='#2d2d2d', fg='white')
        title_label.pack(pady=(0, 20))

        # Year selection
        year_frame = tk.Frame(main_frame, bg='#2d2d2d')
        year_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(year_frame, text="Year:", font=('Segoe UI', 12, 'bold'),
                bg='#2d2d2d', fg='white').pack(side=tk.LEFT, padx=(0, 10))

        year_spinbox = tk.Spinbox(year_frame, from_=2020, to=2030, width=8,
                                 textvariable=selected_year, font=('Segoe UI', 12),
                                 command=lambda: self.update_day_buttons(selected_year.get(),
                                                                        selected_month.get(),
                                                                        day_buttons_frame,
                                                                        selected_day))
        year_spinbox.pack(side=tk.LEFT)

        # Month selection
        month_frame = tk.Frame(main_frame, bg='#2d2d2d')
        month_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(month_frame, text="Month:", font=('Segoe UI', 12, 'bold'),
                bg='#2d2d2d', fg='white').pack(side=tk.LEFT, padx=(0, 10))

        months = ['January', 'February', 'March', 'April', 'May', 'June',
                 'July', 'August', 'September', 'October', 'November', 'December']

        month_buttons_frame = tk.Frame(month_frame, bg='#2d2d2d')
        month_buttons_frame.pack(side=tk.LEFT)

        for i, month in enumerate(months, 1):
            month_btn = tk.Button(month_buttons_frame, text=month[:3],
                                 font=('Segoe UI', 9), width=4, height=1,
                                 command=lambda m=i: self.select_month(m, selected_month,
                                                                      month_buttons_frame,
                                                                      selected_year.get(),
                                                                      day_buttons_frame,
                                                                      selected_day))
            month_btn.grid(row=i//7, column=i%7, padx=1, pady=1)

        # Day selection
        day_label = tk.Label(main_frame, text="Day:", font=('Segoe UI', 12, 'bold'),
                            bg='#2d2d2d', fg='white')
        day_label.pack(anchor='w', pady=(15, 5))

        day_buttons_frame = tk.Frame(main_frame, bg='#2d2d2d')
        day_buttons_frame.pack(fill=tk.X, pady=(0, 20))

        # Initialize day buttons
        self.update_day_buttons(selected_year.get(), selected_month.get(),
                               day_buttons_frame, selected_day)

        # Buttons
        buttons_frame = tk.Frame(main_frame, bg='#2d2d2d')
        buttons_frame.pack(fill=tk.X)

        ok_btn = tk.Button(buttons_frame, text="OK",
                          font=('Segoe UI', 12, 'bold'), bg='#28a745', fg='white',
                          padx=30, pady=10,
                          command=lambda: self.apply_date_selection(dialog, date_type,
                                                                   selected_year.get(),
                                                                   selected_month.get(),
                                                                   selected_day.get()))
        ok_btn.pack(side=tk.LEFT, padx=(0, 10))

        cancel_btn = tk.Button(buttons_frame, text="Cancel",
                              font=('Segoe UI', 12, 'bold'), bg='#6c757d', fg='white',
                              padx=30, pady=10, command=dialog.destroy)
        cancel_btn.pack(side=tk.LEFT)

        # Update month selection display
        self.update_month_buttons(month_buttons_frame, selected_month.get())

    def select_month(self, month, selected_month_var, month_frame, year, day_frame, selected_day_var):
        """Handle month selection"""
        selected_month_var.set(month)
        self.update_month_buttons(month_frame, month)
        self.update_day_buttons(year, month, day_frame, selected_day_var)

    def update_month_buttons(self, month_frame, selected_month):
        """Update month button colors"""
        for widget in month_frame.winfo_children():
            if isinstance(widget, tk.Button):
                widget.config(bg='#404040', fg='white')

        # Highlight selected month
        buttons = month_frame.winfo_children()
        if 0 < selected_month <= len(buttons):
            buttons[selected_month - 1].config(bg='#007bff', fg='white')

    def update_day_buttons(self, year, month, day_frame, selected_day_var):
        """Update day buttons for selected month/year"""
        # Clear existing day buttons
        for widget in day_frame.winfo_children():
            widget.destroy()

        # Calculate days in month
        import calendar
        days_in_month = calendar.monthrange(year, month)[1]

        # Create day buttons
        for day in range(1, days_in_month + 1):
            day_btn = tk.Button(day_frame, text=str(day),
                               font=('Segoe UI', 10), width=3, height=1,
                               command=lambda d=day: self.select_day(d, selected_day_var, day_frame))
            day_btn.grid(row=(day-1)//7, column=(day-1)%7, padx=1, pady=1)

        # Update day selection display
        self.update_day_buttons_display(day_frame, selected_day_var.get())

    def select_day(self, day, selected_day_var, day_frame):
        """Handle day selection"""
        selected_day_var.set(day)
        self.update_day_buttons_display(day_frame, day)

    def update_day_buttons_display(self, day_frame, selected_day):
        """Update day button colors"""
        for widget in day_frame.winfo_children():
            if isinstance(widget, tk.Button):
                if widget.cget('text') == str(selected_day):
                    widget.config(bg='#007bff', fg='white')
                else:
                    widget.config(bg='#404040', fg='white')

    def apply_date_selection(self, dialog, date_type, year, month, day):
        """Apply selected date"""
        try:
            selected_date = datetime(year, month, day)
            date_str = selected_date.strftime('%Y-%m-%d')

            if date_type == 'from':
                self.from_date.set(date_str)
            else:
                self.to_date.set(date_str)

            dialog.destroy()
        except ValueError:
            messagebox.showerror("Error", "Invalid date selected")

    def filter_by_user(self, event=None):
        """Filter sales by selected user - preserves active date filters"""
        selected = self.selected_user.get()

        # Check if today filter is active and preserve it
        if getattr(self, 'today_filter_active', False):
            # Reapply today filter with user filter
            self.apply_combined_filters()
        elif getattr(self, 'date_range_filter_active', False):
            # Reapply date range filter with user filter
            self.apply_combined_filters()
        else:
            # No date filter active, just apply user filter
            if selected == "All Users":
                self.load_sales()
            else:
                filter_condition = ("u.username = ?", [selected])
                self.load_sales(filter_condition)

    def apply_combined_filters(self):
        """Apply combined date and user filters"""
        date_condition = None
        user_condition = None
        combined_params = []

        # Get date filter condition
        if getattr(self, 'today_filter_active', False):
            # Recreate today filter condition
            now = datetime.now()
            start_hour = int(self.today_start_hour.get())
            start_minute = int(self.today_start_minute.get())

            if now.hour < start_hour or (now.hour == start_hour and now.minute < start_minute):
                today_start = datetime(now.year, now.month, now.day, start_hour, start_minute, 0) - timedelta(days=1)
            else:
                today_start = datetime(now.year, now.month, now.day, start_hour, start_minute, 0)

            today_end = today_start + timedelta(hours=24)
            date_condition = "s.date >= ? AND s.date < ?"
            combined_params.extend([today_start.isoformat(), today_end.isoformat()])

        elif getattr(self, 'date_range_filter_active', False):
            # Recreate date range filter condition
            try:
                from_date_str = self.from_date.get()
                to_date_str = self.to_date.get()
                from_date = datetime.strptime(from_date_str, '%Y-%m-%d')
                to_date = datetime.strptime(to_date_str, '%Y-%m-%d') + timedelta(days=1)

                date_condition = "s.date >= ? AND s.date < ?"
                combined_params.extend([from_date.isoformat(), to_date.isoformat()])
            except:
                pass  # Invalid date format, skip date filter

        # Get user filter condition
        selected_user = self.selected_user.get()
        if selected_user != "All Users":
            user_condition = "u.username = ?"
            combined_params.append(selected_user)

        # Combine conditions
        if date_condition and user_condition:
            combined_condition = f"({date_condition}) AND ({user_condition})"
        elif date_condition:
            combined_condition = date_condition
        elif user_condition:
            combined_condition = user_condition
        else:
            # No filters
            self.load_sales()
            return

        # Apply combined filter
        filter_condition = (combined_condition, combined_params)
        self.load_sales(filter_condition)

    def load_users_for_filter(self):
        """Load users for the filter dropdown with safe connection management"""
        from database import safe_db_connection

        try:
            with safe_db_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT DISTINCT username FROM users ORDER BY username")
                users = c.fetchall()

                user_list = ["All Users"] + [user['username'] for user in users]
                self.user_combobox['values'] = user_list

        except Exception as e:
            print(f"Error loading users for filter: {e}")
            # Fallback to just "All Users" if database error
            self.user_combobox['values'] = ["All Users"]

    def show_print_options(self):
        """Show print options dialog"""
        # Create popup window for print options
        options_window = tk.Toplevel(self.root)
        options_window.title("Print Options")
        options_window.geometry("400x200")
        options_window.configure(bg='#2b2b2b')
        options_window.resizable(False, False)

        # Make window modal
        options_window.transient(self.root)
        options_window.grab_set()

        # Center window
        options_window.update_idletasks()
        x = (options_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (options_window.winfo_screenheight() // 2) - (200 // 2)
        options_window.geometry(f"400x200+{x}+{y}")

        # Main frame
        main_frame = tk.Frame(options_window, bg='#2b2b2b')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Title
        title_label = tk.Label(main_frame, text="Choose Print Option",
                              font=('Segoe UI', 14, 'bold'), bg='#2b2b2b', fg='white')
        title_label.pack(pady=(0, 20))

        # Buttons frame
        buttons_frame = tk.Frame(main_frame, bg='#2b2b2b')
        buttons_frame.pack(fill=tk.X, pady=10)

        # Print only button
        print_only_btn = tk.Button(buttons_frame, text="Print Only",
                                  font=('Segoe UI', 12, 'bold'), bg='#17a2b8', fg='white',
                                  padx=20, pady=10, command=lambda: self.execute_print_option('print', options_window))
        print_only_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Email only button
        email_only_btn = tk.Button(buttons_frame, text="Email Only",
                                  font=('Segoe UI', 12, 'bold'), bg='#28a745', fg='white',
                                  padx=20, pady=10, command=lambda: self.execute_print_option('email', options_window))
        email_only_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Both button
        both_btn = tk.Button(buttons_frame, text="Print & Email",
                            font=('Segoe UI', 12, 'bold'), bg='#ff8000', fg='white',
                            padx=20, pady=10, command=lambda: self.execute_print_option('both', options_window))
        both_btn.pack(side=tk.LEFT)

        # Cancel button
        cancel_btn = tk.Button(main_frame, text="Cancel",
                              font=('Segoe UI', 10), bg='#6c757d', fg='white',
                              padx=20, pady=5, command=options_window.destroy)
        cancel_btn.pack(pady=(20, 0))

        # Focus window
        options_window.focus_set()

    def execute_print_option(self, option, window):
        """Execute the selected print option"""
        window.destroy()

        if option == 'print':
            self.print_history()
        elif option == 'email':
            self.email_history()
        elif option == 'both':
            self.print_and_email_history()

    def print_history(self):
        """Print professional sales history report"""
        try:
            # Get current filter information
            filter_info = self.get_current_filter_info()

            # Get current sales data from the tree
            sales_data = self.get_current_sales_data()

            if not sales_data:
                messagebox.showwarning("Warning", "No sales data to print")
                return

            # Generate history receipt
            from receipt_generator import ReceiptGenerator
            receipt_gen = ReceiptGenerator(self.app)

            generated_by = self.app.current_user['username'] if 'username' in self.app.current_user else 'Unknown'

            history_receipt = receipt_gen.generate_history_receipt(
                sales_data, filter_info, generated_by
            )

            # Print the history report
            if receipt_gen.print_receipt(history_receipt, "Sales History Report"):
                messagebox.showinfo("Success", "Sales history report printed successfully!")
            else:
                messagebox.showerror("Error", "Failed to print sales history report")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to print history: {e}")

    def get_current_filter_info(self):
        """Get information about current filters applied"""
        filter_info = {}

        # Check which filter is active and set appropriate date_range
        if getattr(self, 'today_filter_active', False):
            # Today filter is active
            current_time = datetime.now()
            filter_info['date_range'] = f"Today ({current_time.strftime('%d/%m/%Y')})"
        elif getattr(self, 'date_range_filter_active', False):
            # Date range filter is active
            from_date = getattr(self, 'from_date', tk.StringVar()).get()
            to_date = getattr(self, 'to_date', tk.StringVar()).get()
            if from_date and to_date:
                if from_date == to_date:
                    # Single day
                    try:
                        date_obj = datetime.strptime(from_date, '%Y-%m-%d')
                        filter_info['date_range'] = f"Today ({date_obj.strftime('%d/%m/%Y')})"
                    except:
                        filter_info['date_range'] = f"{from_date} to {to_date}"
                else:
                    # Date range
                    filter_info['date_range'] = f"{from_date} to {to_date}"
            else:
                filter_info['date_range'] = "All dates"
        else:
            # No specific filter active
            filter_info['date_range'] = "All dates"

        # Check user filter
        selected_user = getattr(self, 'selected_user', tk.StringVar(value="All Users")).get()
        filter_info['user_filter'] = selected_user

        return filter_info

    def get_current_sales_data(self):
        """Get current sales data from the tree view"""
        sales_data = []

        # Get all items from the tree
        for item_id in self.sales_tree.get_children():
            item_values = self.sales_tree.item(item_id)['values']

            if len(item_values) >= 6:  # Ensure we have all required fields
                # Extract numeric total
                try:
                    total_str = item_values[4]
                    total_amount = float(total_str.replace(' MAD', '')) if ' MAD' in total_str else float(total_str)
                except:
                    total_amount = 0.0

                sale_data = {
                    'id': item_values[0],
                    'date': item_values[1] + ' ' + item_values[2],  # Combine date and time
                    'username': item_values[3],
                    'total': total_amount,
                    'items': item_values[5]
                }
                sales_data.append(sale_data)

        return sales_data

    def delete_all_history(self):
        """Delete all sales history with safe connection management"""
        if messagebox.askyesno(self.app.get_text('confirm'),
                              self.app.get_text('confirm_delete_all_history')):
            from database import safe_db_connection

            try:
                with safe_db_connection() as conn:
                    c = conn.cursor()
                    c.execute("DELETE FROM sale_items")
                    c.execute("DELETE FROM sales")
                    conn.commit()

                    messagebox.showinfo(self.app.get_text('success'),
                                      self.app.get_text('all_history_deleted'))
                    self.load_sales()

            except Exception as e:
                messagebox.showerror(self.app.get_text('error'),
                                   f"{self.app.get_text('failed_delete_history')}: {e}")

    def email_history(self):
        """Email history report as PDF"""
        try:
            from email_manager import EmailManager
            from pdf_generator import PDFGenerator
            import threading

            email_manager = EmailManager()

            # Check if email is enabled
            smtp_config = email_manager.get_smtp_config()
            if not smtp_config['email_enabled']:
                messagebox.showwarning("Email Disabled", "Email feature is disabled. Please configure email settings first.")
                return

            # Check if email addresses are configured
            email_addresses = email_manager.get_email_addresses()
            if not email_addresses:
                messagebox.showwarning("No Recipients", "No email addresses configured. Please add email recipients first.")
                return

            # Show progress dialog
            progress_window = self.show_progress_dialog("Generating PDF and sending email...")

            def email_thread():
                try:
                    # Generate receipt content
                    receipt_content = self.generate_receipt_content()

                    if not receipt_content:
                        self.root.after(0, lambda: self.close_progress_and_show_error(progress_window, "No sales data to email"))
                        return

                    # Generate PDF
                    pdf_generator = PDFGenerator()
                    business_info = self.get_business_info()
                    logo_data = self.get_logo_data()

                    pdf_path = pdf_generator.create_history_pdf(receipt_content, business_info, logo_data)

                    if not pdf_path:
                        self.root.after(0, lambda: self.close_progress_and_show_error(progress_window, "Failed to generate PDF"))
                        return

                    # Prepare email content
                    filter_info = self.get_current_filter_info()
                    subject = f"Sales History Report - {filter_info['period']}"
                    body = f"""Dear Recipient,

Please find attached the sales history report for {filter_info['period']}.

Report Details:
- Period: {filter_info['period']}
- Cashier: {filter_info['cashier']}
- Generated: {filter_info['timestamp']}
- Total Sales: {filter_info['total']}

This report was automatically generated by the POS System.

Best regards,
{business_info.get('business_name', 'POS Business')}"""

                    # Send email
                    success, message = email_manager.send_history_email(pdf_path, subject, body, business_info.get('business_name', 'POS Business'))

                    # Clean up PDF
                    pdf_generator.cleanup_temp_file(pdf_path)

                    # Show result
                    if success:
                        self.root.after(0, lambda: self.close_progress_and_show_success(progress_window, f"Email sent successfully!\n{message}"))
                    else:
                        self.root.after(0, lambda: self.close_progress_and_show_error(progress_window, f"Failed to send email:\n{message}"))

                except Exception as e:
                    self.root.after(0, lambda: self.close_progress_and_show_error(progress_window, f"Error sending email: {str(e)}"))

            # Start email thread
            threading.Thread(target=email_thread, daemon=True).start()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to email history: {str(e)}")

    def print_and_email_history(self):
        """Print and email history report"""
        # First print
        self.print_history()

        # Then email
        self.email_history()

    def show_progress_dialog(self, message):
        """Show progress dialog"""
        progress_window = tk.Toplevel(self.root)
        progress_window.title("Processing...")
        progress_window.geometry("300x100")
        progress_window.configure(bg='#2b2b2b')
        progress_window.resizable(False, False)

        # Make window modal
        progress_window.transient(self.root)
        progress_window.grab_set()

        # Center window
        progress_window.update_idletasks()
        x = (progress_window.winfo_screenwidth() // 2) - (300 // 2)
        y = (progress_window.winfo_screenheight() // 2) - (100 // 2)
        progress_window.geometry(f"300x100+{x}+{y}")

        # Message label
        message_label = tk.Label(progress_window, text=message,
                                font=('Segoe UI', 10), bg='#2b2b2b', fg='white')
        message_label.pack(expand=True)

        # Update display
        progress_window.update()

        return progress_window

    def close_progress_and_show_success(self, progress_window, message):
        """Close progress dialog and show success message"""
        progress_window.destroy()
        messagebox.showinfo("Success", message)

    def close_progress_and_show_error(self, progress_window, message):
        """Close progress dialog and show error message"""
        progress_window.destroy()
        messagebox.showerror("Error", message)

    def get_business_info(self):
        """Get business information for email"""
        try:
            from receipt_generator import ReceiptGenerator
            receipt_gen = ReceiptGenerator()
            settings = receipt_gen.load_receipt_settings()
            return {
                'business_name': settings.get('business_name', ''),
                'business_address': settings.get('business_address', ''),
                'business_phone': settings.get('business_phone', '')
            }
        except:
            return {'business_name': 'POS Business', 'business_address': '', 'business_phone': ''}

    def get_logo_data(self):
        """Get logo data for PDF"""
        try:
            from receipt_generator import ReceiptGenerator
            receipt_gen = ReceiptGenerator()
            settings = receipt_gen.load_receipt_settings()
            return settings.get('logo_image')
        except:
            return None

    def generate_receipt_content(self):
        """Generate receipt content for PDF/email"""
        try:
            # Get current filter information
            filter_info = self.get_current_filter_info()

            # Get current sales data from the tree
            sales_data = self.get_current_sales_data()

            if not sales_data:
                return None

            # Generate the same content as print_history
            from receipt_generator import ReceiptGenerator
            receipt_gen = ReceiptGenerator()

            # Create receipt content
            receipt_content = receipt_gen.generate_history_receipt(
                sales_data,
                filter_info['cashier'],
                filter_info['period']
            )

            return receipt_content

        except Exception as e:
            print(f"Error generating receipt content: {e}")
            return None

    def show_email_settings(self):
        """Show email settings window"""
        from email_settings import EmailSettingsWindow
        EmailSettingsWindow(self.root, self.app)
