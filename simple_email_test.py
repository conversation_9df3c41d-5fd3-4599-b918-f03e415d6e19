#!/usr/bin/env python3
"""
Simple email test
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart

def test_gmail_direct():
    print("=== DIRECT GMAIL TEST ===")
    
    # Gmail credentials
    email = "<EMAIL>"
    password = "wjqm ztpx vhkr nfls"
    
    try:
        print(f"Testing connection to {email}...")
        
        # Create message
        msg = MIMEMultipart()
        msg['From'] = email
        msg['To'] = "<EMAIL>"
        msg['Subject'] = "POS System Test Email"
        
        body = "This is a test email from the POS system."
        msg.attach(MIMEText(body, 'plain'))
        
        # Connect and send
        print("Connecting to Gmail SMTP...")
        context = ssl.create_default_context()
        
        with smtplib.SMTP('smtp.gmail.com', 587) as server:
            print("Starting TLS...")
            server.starttls(context=context)
            
            print("Logging in...")
            server.login(email, password)
            
            print("Sending test email...")
            text = msg.as_string()
            server.sendmail(email, ["<EMAIL>"], text)
            
        print("✅ EMAIL SENT SUCCESSFULLY!")
        print("Gmail account is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ EMAIL FAILED: {e}")
        return False

def test_email_manager():
    print("\n=== EMAIL MANAGER TEST ===")
    
    try:
        from email_manager import EmailManager
        
        em = EmailManager()
        print("Email manager created")
        
        # Test connection
        success, message = em.test_smtp_connection()
        print(f"Connection test: {success}")
        print(f"Message: {message}")
        
        return success
        
    except Exception as e:
        print(f"❌ Email manager test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 SIMPLE EMAIL SYSTEM TEST")
    print("=" * 40)
    
    # Test 1: Direct Gmail
    gmail_works = test_gmail_direct()
    
    # Test 2: Email Manager
    manager_works = test_email_manager()
    
    print("\n" + "=" * 40)
    print("📊 RESULTS")
    print("=" * 40)
    print(f"Direct Gmail: {'✅ WORKING' if gmail_works else '❌ FAILED'}")
    print(f"Email Manager: {'✅ WORKING' if manager_works else '❌ FAILED'}")
    
    if gmail_works and manager_works:
        print("\n🎉 EMAIL SYSTEM IS WORKING!")
    else:
        print("\n❌ EMAIL SYSTEM NEEDS FIXING")
