#!/usr/bin/env python3
"""
Test database sales retrieval directly
"""

def test_database_sales():
    print("=== TESTING DATABASE SALES RETRIEVAL ===")
    
    try:
        from database import safe_db_connection
        
        with safe_db_connection() as conn:
            c = conn.cursor()
            
            # Get sales with user information
            c.execute("""
                SELECT s.id, s.total, s.date, s.items, u.username
                FROM sales s
                LEFT JOIN users u ON s.user_id = u.id
                ORDER BY s.date DESC
                LIMIT 10
            """)
            
            sales = c.fetchall()
            print(f"Found {len(sales)} sales records")
            
            if sales:
                print("Sample sales data:")
                for sale in sales:
                    sale_id, total, date_str, items, username = sale
                    print(f"  ID: {sale_id}, User: {username}, Total: {total:.2f}, Date: {date_str}")
                
                # Format for receipt
                sales_data = []
                for sale in sales:
                    sale_id, total, date_str, items, username = sale
                    
                    # Format date
                    try:
                        from datetime import datetime
                        if isinstance(date_str, str):
                            date_obj = datetime.strptime(date_str[:19], '%Y-%m-%d %H:%M:%S')
                            formatted_date = date_obj.strftime('%d/%m/%Y %H:%M:%S')
                        else:
                            formatted_date = str(date_str)
                    except:
                        formatted_date = str(date_str)
                    
                    sales_data.append({
                        'id': sale_id,
                        'date': formatted_date,
                        'user': username or 'Unknown',
                        'total': f"{total:.2f} MAD",
                        'items': items or 'No items'
                    })
                
                print(f"\n✅ Formatted {len(sales_data)} sales records for receipt")
                return sales_data
            else:
                print("❌ No sales data found")
                return []
                
    except Exception as e:
        print(f"❌ Database error: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_receipt_with_real_data(sales_data):
    print("\n=== TESTING RECEIPT WITH REAL DATA ===")
    
    try:
        if not sales_data:
            print("❌ No sales data provided")
            return False
        
        # Create receipt content
        from datetime import datetime
        
        content_lines = []
        content_lines.append("SALES HISTORY REPORT")
        content_lines.append("=" * 50)
        content_lines.append(f"Period: All Time")
        content_lines.append(f"Cashier: All Users")
        content_lines.append(f"Generated: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
        content_lines.append("")
        content_lines.append("Date/Time           User        Total")
        content_lines.append("-" * 50)
        
        total_amount = 0.0
        for sale in sales_data:
            date_str = sale.get('date', 'Unknown')[:18]
            user_str = sale.get('user', 'Unknown')[:10]
            total_str = str(sale.get('total', '0.00'))
            
            # Extract numeric value
            try:
                if 'MAD' in total_str:
                    numeric_total = float(total_str.replace(' MAD', ''))
                else:
                    numeric_total = float(total_str.replace(' €', '').replace('€', ''))
                total_amount += numeric_total
            except:
                numeric_total = 0.0
            
            line = f"{date_str:<18} {user_str:<10} {total_str}"
            content_lines.append(line)
        
        content_lines.append("-" * 50)
        content_lines.append(f"TOTAL: {total_amount:.2f} MAD")
        content_lines.append("")
        content_lines.append(f"Report contains {len(sales_data)} transaction(s)")
        
        receipt_content = "\n".join(content_lines)
        
        print("✅ Receipt content generated:")
        print("-" * 40)
        print(receipt_content)
        print("-" * 40)
        
        # Test PDF generation
        print("\nTesting PDF generation...")
        from pdf_generator import PDFGenerator
        pdf_gen = PDFGenerator()
        
        business_info = {
            'business_name': 'Le Comptoir POS',
            'business_address': '123 Business Street',
            'business_phone': '+1234567890'
        }
        
        pdf_path = pdf_gen.create_history_pdf(receipt_content, business_info)
        
        if pdf_path:
            print(f"✅ PDF generated: {pdf_path}")
            
            # Test email
            print("\nTesting email with real data...")
            from email_manager import EmailManager
            em = EmailManager()
            
            em.save_smtp_config(True)
            em.add_email_address("<EMAIL>")
            
            subject = "Sales History Report - Real Database Data"
            body = f"""Dear Recipient,

This email contains a REAL sales history report generated from the POS database.

Report Summary:
- Total Records: {len(sales_data)}
- Total Amount: {total_amount:.2f} MAD
- Generated: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}

The system successfully retrieved actual sales data and generated this professional report.

Best regards,
Le Comptoir POS System"""
            
            success, message = em.send_history_email(pdf_path, subject, body)
            print(f"Email result: {success}")
            print(f"Message: {message}")
            
            # Cleanup
            pdf_gen.cleanup_temp_file(pdf_path)
            em.remove_email_address("<EMAIL>")
            
            return success
        else:
            print("❌ PDF generation failed")
            return False
            
    except Exception as e:
        print(f"❌ Receipt generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 DATABASE SALES DATA TEST")
    print("=" * 40)
    
    # Test 1: Get real sales data
    sales_data = test_database_sales()
    
    # Test 2: Generate receipt with real data
    if sales_data:
        receipt_success = test_receipt_with_real_data(sales_data)
    else:
        receipt_success = False
    
    print("\n" + "=" * 40)
    print("📊 RESULTS")
    print("=" * 40)
    
    print(f"Database Sales: {'✅ WORKING' if sales_data else '❌ NO DATA'}")
    print(f"Receipt Generation: {'✅ WORKING' if receipt_success else '❌ FAILED'}")
    
    if sales_data and receipt_success:
        print("\n🎉 SALES DATA ISSUE COMPLETELY FIXED!")
        print("\nThe system now:")
        print("✅ Retrieves real sales from database")
        print("✅ Generates professional reports")
        print("✅ Emails PDF copies successfully")
        print("✅ Works with existing sales data")
        print("\nNo more 'no sales data to print' errors!")
        print("Check 'email_outbox' folder for the email.")
    else:
        print("\n⚠️ PARTIAL SUCCESS")
        if sales_data:
            print("✅ Database has sales data")
        else:
            print("❌ No sales data in database")
        
        if receipt_success:
            print("✅ Receipt generation works")
        else:
            print("❌ Receipt generation failed")
