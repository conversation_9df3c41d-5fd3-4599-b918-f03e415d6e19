#!/usr/bin/env python3
"""
Test the updated email configuration
"""

from email_manager import <PERSON>ailMana<PERSON>

def test_config():
    print("=== EMAIL CONFIGURATION TEST ===")
    
    em = EmailManager()
    config = em.get_smtp_config()
    
    print(f"Email: {config['sender_email']}")
    print(f"Password: {config['sender_password']}")
    print(f"SMTP Server: {config['smtp_server']}")
    print(f"SMTP Port: {config['smtp_port']}")
    
    print("\n=== TESTING SMTP CONNECTION ===")
    success, message = em.test_smtp_connection()
    
    print(f"Connection Test: {'✅ SUCCESS' if success else '❌ FAILED'}")
    print(f"Message: {message}")
    
    if not success:
        print("\n📋 TO FIX:")
        print("1. Go to https://myaccount.google.com")
        print("2. Sign <NAME_EMAIL>")
        print("3. Go to Security → Less secure app access")
        print("4. Turn ON 'Allow less secure apps'")
        print("5. Test again")

if __name__ == "__main__":
    test_config()
