#!/usr/bin/env python3
"""
Test the working email system step by step
"""

def test_step_by_step():
    print("=== STEP-BY-STEP EMAIL TEST ===")
    
    try:
        # Step 1: Import and create email manager
        print("Step 1: Creating email manager...")
        from email_manager import EmailManager
        em = EmailManager()
        print("✅ Email manager created")
        
        # Step 2: Check configuration
        print("\nStep 2: Checking configuration...")
        config = em.get_smtp_config()
        print(f"Email: {config['sender_email']}")
        print(f"SMTP Server: {config['smtp_server']}")
        print(f"SMTP Port: {config['smtp_port']}")
        print("✅ Configuration loaded")
        
        # Step 3: Test SMTP connection
        print("\nStep 3: Testing SMTP connection...")
        success, message = em.test_smtp_connection()
        print(f"Connection result: {success}")
        print(f"Message: {message}")
        
        if not success:
            print("❌ SMTP connection failed - cannot proceed")
            return False
        
        print("✅ SMTP connection successful")
        
        # Step 4: Enable email and add test address
        print("\nStep 4: Configuring email settings...")
        em.save_smtp_config(True)
        
        # Add test email
        test_email = "<EMAIL>"  # Replace with real email for testing
        em.add_email_address(test_email)
        print(f"✅ Added test email: {test_email}")
        
        # Step 5: Test PDF generation
        print("\nStep 5: Testing PDF generation...")
        from pdf_generator import PDFGenerator
        pdf_gen = PDFGenerator()
        
        test_content = """SALES HISTORY REPORT
==================================================
Period: Today
Cashier: admin
Generated: 13/06/2025 17:00:00

Date/Time           User        Total
--------------------------------------------------
13/06/2025 16:55:30 admin       25.50 €
13/06/2025 16:45:20 cashier     18.75 €
--------------------------------------------------
TOTAL: 44.25 €"""
        
        business_info = {
            'business_name': 'Le Comptoir POS',
            'business_address': '123 Business Street',
            'business_phone': '+1234567890'
        }
        
        pdf_path = pdf_gen.create_history_pdf(test_content, business_info)
        
        if pdf_path:
            print(f"✅ PDF generated: {pdf_path}")
        else:
            print("❌ PDF generation failed")
            return False
        
        # Step 6: Test email sending
        print("\nStep 6: Testing email sending...")
        
        subject = "Sales History Report - Working Test"
        body = """Dear Recipient,

This is a test email from the Le Comptoir POS system.

The email system is now working correctly and this PDF attachment 
contains a sample sales history report.

Report Details:
- Period: Today
- Total Sales: 44.25 €
- Generated: 13/06/2025 17:00:00

Best regards,
Le Comptoir POS System
<EMAIL>"""
        
        success, message = em.send_history_email(pdf_path, subject, body, "Le Comptoir POS")
        
        print(f"Email send result: {success}")
        print(f"Message: {message}")
        
        # Step 7: Cleanup
        print("\nStep 7: Cleaning up...")
        pdf_gen.cleanup_temp_file(pdf_path)
        em.remove_email_address(test_email)
        print("✅ Cleanup completed")
        
        if success:
            print("\n🎉 EMAIL SYSTEM FULLY WORKING!")
            print("The POS email system is ready for production use.")
            return True
        else:
            print("\n❌ Email sending failed")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pos_workflow():
    """Test the actual POS workflow"""
    print("\n=== TESTING POS WORKFLOW ===")
    
    try:
        from email_manager import EmailManager
        
        em = EmailManager()
        
        # Enable email
        em.save_smtp_config(True)
        
        # Add real email for testing
        real_email = input("Enter your email address for testing (or press Enter to skip): ").strip()
        
        if real_email:
            em.add_email_address(real_email)
            print(f"✅ Added your email: {real_email}")
            
            # Test the workflow
            print("\nTesting POS workflow...")
            print("1. User goes to Sales History")
            print("2. User clicks 'Print History & Email' button")
            print("3. System prints receipt AND emails PDF")
            
            # Simulate the workflow
            config = em.get_smtp_config()
            if config['email_enabled']:
                recipients = em.get_email_addresses()
                if recipients:
                    print(f"✅ Email enabled with {len(recipients)} recipients")
                    print("✅ POS workflow ready!")
                    
                    # Clean up
                    em.remove_email_address(real_email)
                    return True
                else:
                    print("❌ No recipients configured")
                    return False
            else:
                print("❌ Email not enabled")
                return False
        else:
            print("Skipping real email test")
            return True
            
    except Exception as e:
        print(f"❌ Workflow test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 COMPREHENSIVE EMAIL SYSTEM TEST")
    print("=" * 50)
    
    # Test 1: Step by step functionality
    step_success = test_step_by_step()
    
    # Test 2: POS workflow
    workflow_success = test_pos_workflow()
    
    print("\n" + "=" * 50)
    print("📊 FINAL TEST RESULTS")
    print("=" * 50)
    
    print(f"Email System: {'✅ WORKING' if step_success else '❌ FAILED'}")
    print(f"POS Workflow: {'✅ READY' if workflow_success else '❌ NOT READY'}")
    
    if step_success and workflow_success:
        print("\n🎉 COMPLETE EMAIL SYSTEM WORKING!")
        print("The POS system is ready for production use.")
        print("Users can now print receipts and email PDF copies.")
    else:
        print("\n⚠️ SYSTEM NEEDS DEBUGGING")
        print("Check the detailed output above for specific issues.")
